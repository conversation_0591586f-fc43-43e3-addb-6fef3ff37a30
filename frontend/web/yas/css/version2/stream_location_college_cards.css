 /*------------------------------news carousel-------------------------------------*/
 .carouselDiv {
   box-shadow: none;
   background: var(--color-white);
   border: var(--border-line);
   overflow-wrap: break-word;
   padding: 20px;
   margin: 0 0 20px;
   border-radius: 4px
 }

 .carouselDiv h2 {
   font-size: 18px;
   font-weight: bold;
   line-height: 28px;
   margin-bottom: 15px;
 }

 .sliderslide {
   position: relative;
 }

 .carouselSection {
   display: flex;
   gap: 16px;
   overflow-x: auto;
   scrollbar-width: none;
   padding: 10px 2px;

 }

 .cardUpperBody {
   flex: 0 0 auto;
   max-width: 330px;
   width: 100%;
   background-color: #fff;
   padding: 20px;
   border-radius: 14px;
   box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, .12);
 }

 .college-img {
   width: 100%;
   height: 190px;
   border-radius: 14px 14px 0 0;
 }

 .college-section {
   display: flex;
   margin: 10px 0 10px 0;
   position: relative;
 }

 .college-logo {
   width: 33px;
   height: 33px;
   border-radius: 2px;
   border: 1px solid;
   border-image-slice: 1;
   border-width: 1px;
   border-image-source: linear-gradient(to left, #FF4E534D, #0B66C233);
   margin-right: 10px;
 }

 .college-name {
   width: 100%;
   padding-right: 42px
 }

 .college-name h3 {
   font-size: 16px;
   font-weight: 500;
   line-height: 18px;
 }

 .collegeRating {
   background-color: #26A373;
   width: 36px;
   height: 16px;
   border-radius: 4px;
   font-weight: 500;
   font-size: 12px;
   line-height: 14px;
   display: flex;
   align-items: center;
   justify-content: center;
   color: #fff;
   position: absolute;
   right: 0;
   top: 0;
 }

 .collegeRating>img {
   width: 10px;
   height: 10px;
   display: initial;
   margin-left: 3px;
 }

 .collegeType {
   width: 54px;
   height: 16px;
   border-radius: 4px;
   border: 1px solid;
   border-image-slice: 1;
   border-width: 1px;
   border-image-source: linear-gradient(to left, #FF4E534D, #99CBFE66);
   background-image: linear-gradient(140deg, #99CBFE66, #FF4E5333);
   font-size: 11px !important;
   padding: 2px 5px;
   display: flex;
   justify-content: center;
   align-items: center;

 }

 .collegesection {
   display: flex;
   gap: 5px;
   margin-top: 3px;
 }

 .shortlist {
   width: 100%;
   display: flex;
   margin: 15px 0;
   font-size: 12px !important;
   font-weight: 400;
   gap: 10px;
   align-items: center;
 }

 .shortlist p {
   padding-bottom: 0 !important;
 }

 .btnApply {
   background-color: #FF4E53;
   width: 100%;
   height: 33px;
   border-radius: 4px;
   font-weight: 500;
   font-size: 14px;
   line-height: 16px;
   border: 0;
   color: #fff;
 }

 .bgImg {
   width: 100%;
   height: 100%;
   position: relative;
   display: flex;
   align-items: center;
 }

 .bgImg:after {
   content: '';
   position: absolute;
   top: 0;
   left: 0;
   background-color: rgb(255 255 255 / 90%);
   width: 100%;
   height: 100%;
   border-radius: 14px;
 }

 .bgImg img {
   width: 100%;
   height: 100%;
   border-radius: 14px;
   position: absolute;
 }

 .bgtext {
   position: relative;
   z-index: 9;
   width: 100%;
   height: auto;
   display: flex;
   align-items: center;
   justify-content: center;
   flex-wrap: wrap;
   padding: 40px;
   margin: 0 auto;
 }

 .bgtext h2 {
   font-weight: 500;
   font-size: 18px;
   line-height: 21px;
   text-align: center;
 }

 .bgtext p {
   font-size: 12px;
   font-weight: 400;
   line-height: 18px;
   text-align: center;
   margin-bottom: 15px;
 }

 .college-icon {
   width: 60px;
   height: 60px;
   border-radius: 50%;
   margin-bottom: 15px;
   background: linear-gradient(to right, #99CBFE66, #FF4E5333);
   border: 2px solid transparent;
   background-clip: padding-box, border-box;
   background-origin: border-box;
   background-image:
     linear-gradient(to right, #99CBFE66, #FF4E5333),
     linear-gradient(to left, #FF4E534D, #99CBFE66);
   display: flex;
   align-items: center;
   justify-content: center
 }

 .sliderslide .scrollLeft {
   top: 50%;
   left: -20px;
 }

 .sliderslide .scrollRight {
   right: -20px;
 }

 .sliderslide .spriteIcon.over {
   pointer-events: auto;
   opacity: 1;
 }

 .collegeSvg {
   width: 29px;
   height: 29px;
   background: url(/yas/images/union.webp) no-repeat;
 }


 /*------------------------------news carousel-------------------------------------*/