<?php

use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\FilterHelper;
use common\models\CollegeContent;
use common\models\Lead;
use common\models\LeadBucketTagging;
use common\services\CollegeService;
use common\services\UserService;
use common\services\PopularCollegeService;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use frontend\helpers\Url;

$collegeResults = array_chunk($models, 3);
$i = !empty($iRank) ? $iRank : 1;
$firstSix = 0;
$ad = 1;

?>

<div class="searchedcollegeList">
    <div class="row" id="sponsor_list_slot1"></div>
    <?php $cta_data = [];
    $popularCollegeCardsDisplayed = false;
    foreach ($collegeResults as $k => $models): ?>
        <!-- append ad after 3rd row -->
        <?php if ($ad++ % 3 == 0): ?>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if (!$isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x600')
                        ?>
                    <?php else: ?>
                        <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x250')
                        ?>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Display Popular College Cards after 3rd card -->
        <?php if ($k == 0 && !$popularCollegeCardsDisplayed):
            $popularCollegeService = new PopularCollegeService();

            // Extract stream_id from URL or request parameters
            $streamId = null;
            if (!empty($stream)) {
                $streamId = $stream; // If stream is passed as parameter
            } elseif (!empty(Yii::$app->request->get('stream'))) {
                $streamId = Yii::$app->request->get('stream');
            }

            $streamLocationColleges = $popularCollegeService->getCollegeCards(
                $streamId,
                null, // No specific degree for listing
                null  // State from IP
            );
            if (!empty($streamLocationColleges)):
                $popularCollegeCardsDisplayed = true;
                ?>
                <?= $this->render('../../layouts/_stream_location_college_cards', [
                'colleges' => $streamLocationColleges,
                'title' => 'Popular ' . (!empty($streamLocationColleges[0]['stream_name']) ? $streamLocationColleges[0]['stream_name'] : 'Engineering') . ' Colleges Near You'
            ]) ?>
            <?php endif;
        endif; ?>

        <div class="row">
            <?php foreach ($models as $model):
                $collegeContentPageStatus = CollegeService::getStatus($model['college_id']);
                $dynamicCta = UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGE_LISTING, 'college-listing', $model['display_name'] ?? $model['name'], $model['slug'], $model['city_name']);
                $ctaLocation1 = $isMobile ? $dynamicCta['cta_position_0']['wap'] : (isset($dynamicCta['cta_position_0']['web']) ? $dynamicCta['cta_position_0']['web'] : '');
                $ctaLocation2 = $isMobile ? $dynamicCta['cta_position_1']['wap'] : (isset($dynamicCta['cta_position_1']['web']) ? $dynamicCta['cta_position_1']['web'] : '');
                $streamSlug = '';

                if (!empty($searchModel->stream) && !empty($searchModel->course)) {
                    $courseId = $model['course']['course_id'] ?? '';
                    $courseName = $model['course']['course_name'] ?? '';
                    $courseSlug = $model['course']['course_slug'] ?? '';
                    $streamSlug = $searchModel->stream ?? '';
                } elseif (!empty($searchModel->stream)) {
                    $courseId = '';
                    $courseName = '';
                    $courseSlug = '';
                    $streamSlug = $searchModel->stream ?? '';
                }

                $firstSix++;
                $content = !empty($model['review']) ? substr($model['review'], 0, 63) : '';
                $reviewContent = !empty($content) ? ContentHelper::removeStyleTag(stripslashes(html_entity_decode($content))) : '';
                $cta1 = $isMobile ? 'colleges_listing_wap_card_left_cta' : 'colleges_listing_web_card_left_cta';
                $cta2 = $isMobile ? 'colleges_listing_wap_card_right_cta' : 'colleges_listing_web_card_right_cta';
                ?>
                <div class="collegeInfoCard">
                    <div class="clgInfoCardHeader <?= ($firstSix > 6) ? 'lazyload' : '' ?>" style="background-image: url(<?php echo !empty($model['banner_image']) ? Url::getCollegeBannerImage($model['banner_image']) : Url::toDefaultCollegeBanner() ?>);" onclick="gmu.url.goto('<?= Url::toRoute($model['slug']) ?>')">
                        <?php if (!empty($model['rank']) && ($model['rank'] <= 200)): ?>
                            <p class="rankingLabel"><?= '#' . $i . ' Rank' ?></p>
                            <?php $i++;
                        endif;  ?>
                        <div class="row">
                            <div class="collegeLogo">
                                <img width="56" height="56" class="<?= ($firstSix > 6) ? 'lazyload' : '' ?>" loading="<?= ($firstSix > 7) ? 'lazy' : '' ?>" src="<?= !empty($model['logo']) ? Url::getCollegeLogo($model['logo']) : Url::defaultCollegeLogo() ?>" onclick="gmu.url.goto('<?= Url::toRoute($model['slug']) ?>')" alt="">
                            </div>
                            <?php if (!$isMobile): ?>
                                <h3 class="collegeName"><a href="<?= Url::toRoute($model['slug']) ?>" title="<?= $model['display_name'] ?? $model['name'] ?>"><?= $model['display_name'] ?? $model['name'] ?></a></h3>
                            <?php endif; ?>
                        </div>
                    </div>
                    </a>
                    <div class=" aboutCollege">
                        <!-- <span class="spriteIcon favriteIcon"></span> -->
                        <?php if ($isMobile): ?>
                            <h3 class="collegeName"><a href="<?= Url::toRoute($model['slug']) ?>"><?= $model['display_name'] ?? $model['name'] ?></a></h3>
                        <?php endif; ?>
                        <p class="collegeLocation"><i class="spriteIcon locationIconBlue"></i><?= $model['city_name'] ?>, <?= $model['state_name'] ?></p>
                        <span class="bulletIcon"></span>
                        <div class="collegeRating"><?= ($model['rating'] == 5) ? '5.0 ' : (empty($model['rating']) ? '' : $model['rating'] . ' '); ?>
                            <?= empty($model['rating']) ? CollegeHelper::getTotalStars(0) : CollegeHelper::getTotalStars($model['rating']) ?>
                            <?php /* if (!empty($model['rev_category_rating'])): ?>
                                <span class="spriteIcon tooltipIcon tooltipAngle">
                                    <span class="tooltipIconText">
                                        <?php foreach ($model['rev_category_rating'] as $key => $value): ?>
                                            <div class="row">
                                                <span class="col-md-6 col-7"><?= $key ?></span>
                                                <span class="col-md-6 col-5"><?= $value ?></span>
                                            </div>
                                        <?php endforeach; ?>
                                    </span>
                                </span>
                            <?php endif;*/ ?>
                            <?php if (!empty($model['review_count'])):  ?>
                                <?php if (!empty($collegeContentPageStatus) && isset($collegeContentPageStatus['reviews']) && $collegeContentPageStatus['reviews'] == CollegeContent::STATUS_ACTIVE): ?>
                                    (<a href="<?= Url::toCollege($model['slug'], 'reviews') ?>"><?= $model['review_count'] ?> Reviews </a>)
                                <?php else: ?>
                                    (<?= $model['review_count'] ?> Reviews)
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                        <?php /*<p class="collegeIntroText">
                            <?php if (!empty($reviewContent) && !empty($collegeContentPageStatus) && isset($collegeContentPageStatus['reviews']) && $collegeContentPageStatus['reviews'] == CollegeContent::STATUS_ACTIVE): ?>
                                <?= strip_tags($reviewContent . '...') ?>
                                <a title="<?= ($model['display_name'] ?? $model['name']) . ' Reviews' ?>" href="<?= Url::toCollege($model['slug'], 'reviews') ?>">
                                    Read More
                                </a>
                            <?php endif; ?>
                        </p>*/ ?>
                        <div class="row collegeKeyinfo">
                            <div class="keyInfo">
                                <p>Average Fees </p>
                                <p><?php if (!empty($model['courseCount']) && $model['courseCount'] != 0) {
                                    if (isset($model['avgFees'])) {
                                        echo '₹' . CollegeHelper::feesFormat($model['avgFees']);
                                    } else {
                                        echo '--';
                                    };
                                   } else {
                                       echo '--';
                                   } ?>
                                </p>
                            </div>
                            <div class="keyInfo">
                                <?php if (!empty($model['course'])  &&  $model['courseCount'] != 0): ?>
                                    <p>No. of Courses Offered</p>
                                    <?php if (!empty($collegeContentPageStatus) && isset($collegeContentPageStatus['courses-fees']) && $collegeContentPageStatus['courses-fees'] == CollegeContent::STATUS_ACTIVE): ?>
                                        <p> <a title="<?= ($model['display_name'] ?? $model['name']) . ' Courses & Fee Details' ?>" href=" <?= Url::toCollege($model['slug'], 'courses-fees') ?>">
                                                <?= isset($model['courseCount']) ? $model['courseCount'] : count($model['course']) ?> Courses
                                            </a>
                                        </p>
                                    <?php else: ?>
                                        <p title="<?= ($model['display_name'] ?? $model['name']) . ' Courses & Fee Details' ?>"><?= isset($model['courseCount']) ? $model['courseCount'] : count($model['course']) ?> Courses</p>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>

                            <div class="keyInfo">
                                <p>Median Salary</p>
                                <p><?= !empty($model['median_salary']) ? '₹' . CollegeHelper::feesFormat(floor($model['median_salary'])) : '--' ?></p>
                            </div>
                            <div class="keyInfo">
                                <p>Exams</p>
                                <?php $examArr = [];
                                if (!empty($model['exams'])):
                                    $examArr = FilterHelper::getExams($model['exams']);
                                    if (!empty($examArr)):
                                        ?>
                                        <p>
                                            <span class="moreCourse">
                                                <?php $count = 0; ?>
                                                <?php foreach ($examArr as $val): ?>
                                                    <span class="<?= $count >= 2 ? 'hideExamFilter ' . $model['slug'] . '_hideExamFilter' : '' ?>"><?= $val ?>
                                                        <?php $count++; ?>
                                                        <?php if ($count != count($examArr)) {
                                                            echo ',';
                                                        } ?>
                                                    </span>

                                                <?php endforeach; ?>
                                            </span>
                                            <?php if (count($examArr) > 2): ?>
                                                <span class="examMoreFilter <?= $model['slug'] ?>Exam" data-id="<?= $model['slug'] ?>">+<?= (count($examArr) - 3 == 0) ? 1 : count($examArr) - 2  ?></span>
                                            <?php endif; ?>
                                        </p>
                                    <?php else: ?>
                                        <p>--</p>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <p>--</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="collegeCriteria">
                        <?php if (!empty($model['sub_pages'])): ?>
                            <?= FilterHelper::getSubpages($model['slug'], $model['sub_pages'], $model['display_name'] ?? $model['name']) ?>
                        <?php endif; ?>
                    </div>
                    <div class="clgInfoCardfooter">
                        <div class="lead-cta-college-filter-1 leadFilterData" data-stateId="<?= $model['state_id'] ?>" data-cityId="<?= $model['city_id'] ?>" data-entity="college"
                            data-filter="college-listing" data-lead_cta="23" data-slug="<?= $model['college_id'] ?>" data-course="<?= $courseId ?? '' ?>"
                            data-stream="<?= $streamSlug ?>" data-ctaLocation="<?= $ctaLocation1 ?>" data-title="<?= $dynamicCta['cta_position_0']['lead_form_title'] ?>"
                            data-description="<?= $dynamicCta['cta_position_0']['lead_form_description'] ?>" data-image="<?= Url::getCollegeLogo($model['logo']) ?>"></div>
                        <div class="lead-cta-college-filter-2 leadFilterData" data-entity="college" data-filter="college-listing" data-lead_cta="21"
                            data-slug="<?= $model['college_id'] ?>" data-stateId="<?= $model['state_id'] ?>" data-cityId="<?= $model['city_id'] ?>"
                            data-course="<?= $courseId ?? '' ?>" data-stream="<?= $streamSlug ?>" data-ctaLocation="<?= $ctaLocation2 ?>"
                            data-title="<?= $dynamicCta['cta_position_1']['lead_form_title'] ?>" data-description="<?= $dynamicCta['cta_position_1']['lead_form_description'] ?>"
                            data-image="<?= Url::getCollegeLogo($model['logo']) ?>"></div>
                    </div>
                </div>

            <?php endforeach; ?>
        </div>

        <?php if ($k == 1):
            ?>
            <div class="row" id="sponsor_list_slot2"></div>
        <?php endif; ?>
    <?php endforeach; ?>
    <div class="row" id="sponsor_list_slot3"></div>
</div>
<div class="loadMoreColleges">
    <?php if ($hasNext): ?>
        <p class="col-12 loadMoreList" hasnesxt="<?= $hasNext; ?>" data-irank="<?= $i; ?>" data-page="<?= $page ?>">LOAD MORE <span class="spriteIcon redCaret"></span></p>
    <?php endif; ?>
</div>