<?php

use common\models\College;
use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\CollegeRanksDataHelper;
use common\helpers\DataHelper;
use common\models\LiveUpdate;
use common\services\CollegeService;
use common\services\PopularCollegeService;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use yii\widgets\ListView;

$seoInfoMenu = ['info', 'courses-fees', 'facilities', 'ranking'];
$seoFinalMenu = array_intersect_key($menus, array_flip($seoInfoMenu));

$defaultSeoInfo = ContentHelper::getCollegeDefaultSeoInfo(
    !empty($college->display_name) ? $college->display_name : $college->name,
    'info',
    $seoFinalMenu,
    ['contact-details' => $college->latitude, 'latest-updates' => $recentActivity, 'fees' => $collegeCourses['courses']],
    (!empty($city) ? $city->name : '')
);
$defaultTitle = $college->display_name . ': Ranking, Courses, Fees, Admission, Placements';
$this->title = !empty($content['info']->meta_title) ? CollegeHelper::parseContent($content['info']->meta_title) : $defaultSeoInfo['title'];
$this->context->description = !empty($content['info']->meta_description) ? CollegeHelper::parseContent($content['info']->meta_description) : $defaultSeoInfo['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->context->ogImage = !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner();

$authorImage = !empty($content->author->profile->image) ? (Yii::getAlias('@profileDPFrontend') . '/' . $content->author->profile->image) : '/yas/images/usericon.png';
$authorName =  !empty($content->author) ? $content->author->name : '';

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];

$url = (new CollegeService)->checkFilterPageStatus([], !empty($city) ? $city->slug : '', '', '') == College::SPONSORED_NO ? '/all-colleges/' . (!empty($city) ? $city->slug : '') : '';
if (!empty($url) && !empty($city)) {
    $this->params['breadcrumbs'][] = ['label' => 'Colleges in ' . (!empty($city) ? $city->name : ''), 'url' => [$url], 'title' => 'Colleges in ' . $city->name];
}
$this->params['breadcrumbs'][] = !empty($college->display_name) ? $college->display_name : $college->name;

$this->registerLinkTag(['href' => !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
$this->registerLinkTag(['href' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
if (!empty($authorImage)) {
    $this->registerLinkTag(['href' => $authorImage, 'rel' => 'preload', 'as' => 'image', 'imagesrcset' => $authorImage . ' 300w', 'imagesizes' => '50vw']);
}

// page specific assets

$this->registerCssFile(Yii::$app->params['cssPath'] . 'college-new.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');

//gmu params
$this->params['entity'] = College::ENTITY_COLLEGE;
$this->params['course_count'] = !empty($courseCount) ? $courseCount : '';
$this->params['entity_id'] = $college->id ?? 0;
$this->params['interested_location'] = $city->id ?? null;
$this->params['entity_name'] = $college->name ?? '';
$this->params['entitySlug'] = $college->slug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $college->display_name ?? '';
$this->params['pageName'] = 'info';

// schema
$this->params['schema'] = CollegeHelper::collegeSchema($college, $revCategoryRating ?? [], $reviewCount);
if (!empty($faqs)) {
    $this->params['schema1'] = CollegeHelper::faqSchema($faqs);
}
$courses = !empty($collegeCourses['courses']) ? CollegeHelper::getNonEmptyCourse($collegeCourses['courses'], 'info') : [];
if (isset($recognised)) {
    $recognised = explode(',', $recognised);
}

if (isset($approval)) {
    $approval = explode(',', $approval);
}

?>
<!-- header -->
<?= $this->render('partials/_header-new', [
    'menus' => $menus ?? [],
    'college' => $college,
    'content' => $content['info'],
    'pageName' => 'info',
    'rating' => $revCategoryRating ? CollegeHelper::getTotalRating($revCategoryRating) : '',
    'type' => $type,
    'approval' => $approval,
    'brochure' => $brochure,
    'heading' => !empty($content['info']->h1) ? CollegeHelper::parseContent($content['info']->h1) : (empty($defaultSeoInfo['h1']) ? $defaultSeoInfo['title'] : $defaultSeoInfo['h1']),
    'defaultTitle' => $defaultTitle,
    'categoryRating' => $revCategoryRating,
    'city' => $city,
    'state' => $state,
    'reviewCount' => $reviewCount,
    'author' => $authorDetail,
    'profile' => $profile,
    'dynamicCta' => isset($dynamicCta) && !empty($dynamicCta) ? $dynamicCta : [],
    'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : '',
]) ?>
<?php
/* if (!empty($sponsorClientUrl->redirection_link)):?>
    <?= $this->render('partials/_notification', [
    'sponsorClientUrl' => $sponsorClientUrl,
    'college' => $college,
    ]) ?>
<?php endif;*/ ?>

<!-- page specific navigation -->
<?= $this->render('partials/_menu-card', [
    'menus' => $menus,
    'college' => $college,
    'pageName' => 'info',
    'dropdown' => $dropdowns
]) ?>

<div class="row">
    <div class="col-md-8">
        <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU() && !$isMobile): ?>
            <aside>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                        <?php echo Ad::unit('GMU_COLLEGE_INFO_WEB_728x90_ATF', '[728,90]') ?>
                    </div>
                </div>
            </aside>
        <?php endif; ?>
        <?php if (!empty($collegeNotificationUpdate)): ?>
            <div class="pageData <?= count($collegeNotificationUpdate) > 5  ? 'pageInfo' : '' ?>">
                <div class="infoPage">
                    <?= $this->render('../partials/_collegeNotificationUpdate', [
                        'collegeNotificationUpdate' => $collegeNotificationUpdate,
                        'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' About',
                        'isShowTitle' => true
                    ]) ?>
                </div>
            </div>
        <?php endif; ?>
        <?php if (!empty($content['info']->content)): ?>
            <div class="infoPage">
                <?= $this->render('partials/_main-content-card', [
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' About',
                    'content' => CollegeHelper::parseContent(ContentHelper::htmlDecode($content['info']->content)),
                    'contentAuthor' => $content['info'],
                    'author' => $authorDetail,
                    'profile' => $profile,
                    'recentActivity' => $recentActivity,
                    'examContentTemplate' => $examContentTemplate,
                    'collegeNotificationUpdate' => $collegeNotificationUpdate,
                ]) ?>
            </div>
        <?php else:
            if (!empty($contentTemplate)): ?>
                <div class="infoPage">
                        <?= $this->render('partials/_main-content-card', [
                        'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' About',
                        'content' => CollegeHelper::parseContent(ContentHelper::htmlDecode($contentTemplate)),
                        'contentAuthor' => $content['info'],
                        'author' => $authorDetail,
                        'profile' => $profile,
                        'recentActivity' => $recentActivity,
                        'examContentTemplate' => $examContentTemplate,
                        'collegeNotificationUpdate' => $collegeNotificationUpdate,
                ]) ?>
                </div>
            <?php endif;
        endif; ?>

        <?php /* if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU() && $isMobile): ?>
            <aside>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                        <?php echo Ad::unit('GMU_COLLEGE_INFO_WAP_300x100_ATF', '[300,100]') ?>
                    </div>
                </div>
            </aside>
        <?php endif; */ ?>

        <?php if (isset($featuresGroup['Highlights'])): ?>
            <div class="pageData collegeHeighlights">
                <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> Highlights</h2>
                <table class="courseFeesTable">
                    <tbody>
                        <?php if (!empty($parentCollege)): ?>
                            <tr>
                                <td style="font-weight:400;"><span class="spriteIcon heighlightsIcons3"></span> Affiliate University </td>
                                <td><?= $parentCollege['display_name'] ?? $parentCollege['name'] ?></td>
                            </tr>
                        <?php endif; ?>
                        <?php foreach ($featuresGroup['Highlights'] as $highlight): ?>
                            <?php if (isset(CollegeHelper::$collegeHighlights[$highlight['featureName']])): ?>
                                <tr>
                                    <?php if (!empty($highlight['value'])): ?>
                                        <td style="font-weight:400;"><span class="spriteIcon <?= CollegeHelper::$collegeHighlights[$highlight['featureName']] ?>"></span> <?= $highlight['featureName'] ?></td>
                                        <td><?= ucfirst($highlight['value']) ?></td>
                                    <?php endif; ?>
                                </tr>
                            <?php endif; ?>
                        <?php endforeach; ?>
                        <?php if (!empty($college->url)):
                            //$urlPath = empty(parse_url($college->url, PHP_URL_SCHEME)) ? 'https://' . $college->url : $college->url;
                            ?>
                            <tr>
                                <td><span class="spriteIcon newLinkIcon"></span> Website</td>
                                <td><?= $college->url ?></td>
                            </tr>
                        <?php endif; ?>
                        <?php if (!empty($accredited)): ?>
                            <tr>
                                <td style="font-weight:400;"><span class="spriteIcon heighlightsIcons3"></span> Accredited by </td>
                                <td><?= $accredited ?></td>
                            </tr>
                        <?php endif; ?>
                        <?php if (!empty($recognised)): ?>
                            <tr>
                                <td style="font-weight:400;"><span class="spriteIcon heighlightsIcons3"></span>Recognised by </td>
                                <td>
                                    <span class="moreCourse">
                                        <?php foreach ($recognised as $key => $val): ?>
                                            <span class="<?= $key > 1 ? 'hideExam recognised_hideExam' : '' ?>"><?= $val ?></span>
                                        <?php endforeach; ?>
                                    </span>
                                    <?php if (count($recognised) > 1): ?>
                                        <span class="examMore recognisedExam" data-id="recognised">+<?= count($recognised) - 1 ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php if (!empty($studentDiversityData) && !in_array($college->slug, ['indian-institute-of-fashion-technology-iift-chamarajnagar'])): ?>
            <div class="pageData collegeRankings">
                <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> Student Diversity</h2>
                <table class="courseFeesTable">
                    <thead>
                        <tr>
                            <td>Category</td>
                            <?php if (!empty($studentDiversityData['category'])): ?>
                                <?php foreach ($studentDiversityData['category'] as $key): ?>
                                    <td><?= $key ?></td>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $countCategory = count($studentDiversityData['category']);
                        unset($studentDiversityData['category']);
                        foreach ($studentDiversityData as $key => $val): ?>
                            <tr>
                                <td><?= ($key == 'All') ? 'Total' : $key ?></td>
                                <?php
                                $rowcount = count($val);
                                $leftColumn = $countCategory - $rowcount;
                                foreach ($val as $k => $v): ?>
                                    <td><?= !empty($v) ?  $v :  '-' ?></td>
                                <?php endforeach;
                                if ($leftColumn !== 0):
                                    for ($i = 0; $i < $leftColumn; $i++):
                                        ?>
                                        <td style="text-align: center;">-</td>
                                    <?php endfor;
                                endif;
                                ?>
                            </tr>
                        <?php endforeach; ?>

                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php if (!empty($collegeStudentStaffData) && !in_array($college->slug, ['indian-institute-of-fashion-technology-iift-chamarajnagar'])): ?>
            <div class="pageData collegeRankings">
                <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> PTR Table</h2>
                <table class="courseFeesTable rankTable">
                    <thead>
                        <tr>
                            <td>Particulars</td>
                            <td>Count</td>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($collegeStudentStaffData as $key => $val):
                            foreach ($val as $k => $v):
                                if ($k !== 'Total'):
                                    ?>
                                    <tr>
                                        <td><?= $key ?>(<?= $k ?>)</td>
                                        <td><?= $v['count'] ?></td>
                                    </tr>
                                <?php else: ?>
                                    <tr>
                                        <td><?= $key ?> Total </td>
                                        <td><?= $v ?></td>
                                    </tr>
                                <?php endif;
                            endforeach; ?>
                        <?php endforeach; ?>

                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <!-- Rankings -->
        <?php if (!empty($ranking)): ?>
            <div class="pageData collegeRankings <?= count($ranking) > 2 ? 'pageInfo' : '' ?>">
                <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> Rankings</h2>
                <table class="courseFeesTable rankTable">
                    <thead>
                        <tr>
                            <td>Publisher</td>
                            <td>Ranking & Year</td>
                            <td>Criteria</td>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($ranking as $rank): ?>
                            <tr>
                                <td><img width="80" height="23" src="<?php echo DataHelper::s3Path(null, 'ranking', 'path') . '/' . $rank->publisher->publisher_image ?>" class="lazyload" alt=""> </td>
                                <td>#<?= $rank->rank ?> in <?= $rank->year ?></td>
                                <td><?= CollegeRanksDataHelper::getCriteria($rank->criteria, $rank->criteria_id);  ?></td>
                            </tr>
                        <?php endforeach; ?>

                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php if (!empty($courses)): //dd($courses);
            ?>
            <?php if ($coursesAvgFeesCount != 0): //dd($courses);
                ?>
                <div class="pageData clgCourseAndFeeSection">
                    <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> Courses & Fees <?= date('Y') ?></h2>
                    <table class="courseFeesTable">
                        <thead>
                            <tr>
                                <td>Course</td>
                                <td>Average Fees</td>
                                <td>Duration</td>
                                <td>Action</td>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1;
                            foreach ($courses as $key => $value):
                                if (in_array($value['slug'], ['pgdm', 'diploma', 'certificate', 'fellowship-programme'])) {
                                    continue;
                                }
                                if ($count < 11):
                                    ?>
                                    <tr>
                                        <?php if (!empty($value['coursePage'])): ?>
                                            <td>
                                                <a href="<?= Url::base() . $value['coursePage'] ?>" title="<?= !empty($college->display_name) ? $college->display_name : $college->name ?>  <?= $value['short_name'] ?? $value['name'] ?>">
                                                    <?= $value['short_name'] ?? $value['name'] ?>
                                                </a>
                                            </td>
                                        <?php else: ?>
                                            <td>
                                                <a><?= $value['short_name'] ?? $value['name'] ?></a>
                                            </td>
                                        <?php endif; ?>
                                        <td>
                                            <?= !empty($value['avgFees']) ? '₹' . ContentHelper::indMoneyFormat($value['avgFees']) : '--' ?>
                                        </td>
                                        <td><?= !empty($value['course_duration']) ? CollegeHelper::yearsFormat($value['course_duration']) : '--' ?></td>
                                        <td>
                                            <div class="lead-cta college-info-cta" id="<?= $value['course_id'] ?>" data-slug="<?= $value['slug'] . '-28' ?>" data-lead_cta="28" data-image="<?= $college->logo_image ?? '' ?>" data-entity="college"></div>
                                        </td>
                                    </tr>
                                <?php endif;
                                $count++;
                            endforeach; ?>
                        </tbody>
                    </table>
                    <?php if (!is_numeric($menus['courses-fees'])): ?>
                        <div class="moreCoursesLinkContainer">
                            <a class="moreCoursesLink" title="<?= CollegeHelper::subPageTitle($college, 'courses-fees') ?>" href="<?= Url::toCollege($college->slug, 'courses-fees') ?>">
                                More Courses<span class="spriteIcon urlIcon"></span>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_336x280', '__240x400 __336x280') ?>
                            <?php else: ?>
                                <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280') ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </aside>
            <?php endif;
        endif;
        ?>
        <?php if (!empty($collegeCourses['courses'])): ?>
            <section class="filterSection filtersAll">
                <h2 class="filterHeading"><?= (!empty($college->display_name) ? $college->display_name : $college->name) ?> Courses and Fee Structure</h2>
                <?= $this->render('_filter-new', [
                    'model' => $collegeCourses['searchModel'],
                    'college' => $college,
                    'programList' => $collegeCourses['programList'],
                ]) ?>
            </section>
            <div class="courseTypeList">
                <div class="courseListContainer <?= count($collegeCourses['courses']) > 5 ? 'pageInfo' : '' ?> showMoreScroll">
                    <h2>Courses offered by <?= !empty($college->display_name) ? $college->display_name : $college->name ?></h2>
                    <?php
                    echo ListView::widget([
                        'dataProvider' => $collegeCourses['dataProvider'],
                        'itemView' => function ($model, $key, $index, $widget) use ($collegeCourses, $college, $isMobile, $sponsorClientUrl, $dynamicCta) {
                            $itemContent = $this->render(
                                'partials/_course-info-filter-card',
                                [
                                    'models' => $model,
                                    'course' => $key,
                                    'fullView' => true,
                                    // 'context' => 'main-page',
                                    'college' => $college,
                                    'stateId' => !empty($college->city) ? ($college->city->state->old_id ?? '') : '',
                                    'isMobile' => $isMobile,
                                    'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : '',
                                    'courseList' => $collegeCourses['courses'],
                                    'dynamicCta' => $dynamicCta ?? [],
                                ]
                            );
                            return $itemContent;
                        },
                        'layout' => '{items}',
                        'emptyText' => 'No Results Found',
                    ]);
                    ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if (!empty($cutOff['dataProvider']->allModels)): ?>
            <?= $this->render('partials/_college_course_cutoff', [
                'cutOff' => $cutOff,
                'college' => $college,
                'models' => $cutOff['dataProvider']->allModels,
                'state' => $state,
                'isMobile' => $isMobile,
                'page' => 'info'
            ]) ?>
        <?php endif; ?>

        <?php if ($liveApplicationForm && $isMobile && $college->is_sponsored == College::SPONSORED_NO && Url::toDomain() !=  Url::toBridgeU()): ?>
            <div id="liveApplicationForm"></div>
        <?php endif; ?>

        <?php if (!empty($collegeExams)): ?>
            <?= $this->render('partials/_college-exam', [
                'title' => 'Exams Accepted by ' . (!empty($college->display_name) ? $college->display_name : $college->name),
                'collegeExams' => $collegeExams
            ]) ?>
        <?php endif; ?>

        <?php if (!empty($facilitie->content) && !empty($facilities)): ?>
            <?= $this->render('partials/_content-card', [
                'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Infrastructure',
                'content' =>  $facilitie->content,
                'contentUrl' => Url::toCollege($college->slug, 'facilities'),
                'facilities' => $facilities,
                'readMoreTitle' => CollegeHelper::subPageTitle($college, 'facilities')
            ]) ?>
        <?php endif; ?>

        <?php if (!empty($faqs)): ?>
            <?= $this->render('partials/_faq-card', [
                'faqs' => $faqs,
                // 'college' => $college,
                'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' FAQs',
                'displayName' => (!empty($college->display_name) ? $college->display_name : $college->name),
                'pageName' => $pageName ?? ''
            ]) ?>
            <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
                <aside>
                    <div class="horizontalRectangle desktopOnly">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php echo Freestartads::unit('getmyuni-com_incontent_leaderboard_1', '__728x90') ?>
                        </div>
                    </div>
                </aside>
            <?php endif;
        endif;
        ?>

        <div class="removeFixedQuickLink">
            <!-- Do not Delete this -->
        </div>
        <!-- reviews widget -->
        <?php if (!empty($reviews)): ?>
            <?= $this->render('partials/_review-card', [
                'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Reviews',
                'reviews' => $reviews,
                'collegeId' => $college->id,
                'distrbutionRating' => $revDistributionRating,
                'categoryRating' => $revCategoryRating,
                'viewAllURL' => !empty($menus) && !is_numeric($menus['reviews']) ? Url::toCollege($college->slug, 'reviews') : '',
                'reviewCount' => $reviewCount ?? '',
                'category' => 5
            ]) ?>
        <?php endif; ?>
    </div>

    <div class="col-md-4">
        <aside class="aside-college">
            <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                <div class="lead-cta lead-cta-cls" data-lead_cta="20" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>"></div>
            <?php endif; ?>
            <?php if (!empty($college->parent_id)): ?>
                <?= $this->render('partials/_main-campus', [
                    'college' => $parentCollege,
                ]) ?>
            <?php endif; ?>

            <?php if ($liveApplicationForm && !$isMobile && $college->is_sponsored == College::SPONSORED_NO && Url::toDomain() !=  Url::toBridgeU()): ?>
                <div id="liveApplicationForm"></div>
            <?php endif; ?>

            <?php /*if (!empty($featuredNews) || !empty($recentNews)): ?>
                <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                    'featured' => $featuredNews,
                    'recents' => $recentNews,
                    'isAmp'  => 0,
                    'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                    'smallIcone' => 1
                ]); ?>
            <?php endif; ?>

            <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                    'trendings' => $trendingArticles,
                    'recentArticles' => $recentArticles,
                ]); ?>
            <?php endif; */ ?>
        </aside>
        <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
            <?=
            $this->render('partials/_sidebar-ads.php', [
                'ads' => [
                    ['slot' => 'GMU_COLLEGE_INFO_WEB_300x250_MTF_1', 'size' => '[300, 250]', 'isMobile' => false],
                    ['slot' => 'GMU_COLLEGE_INFO_WEB_300x250_MTF_2', 'size' => '[300, 250]', 'isMobile' => false],
                    ['slot' => 'GMU_COLLEGE_INFO_WAP_300x250_MTF_1', 'size' => '[300, 250]', 'isMobile' => true],
                    // ['slot' => 'GMU_COLLEGE_INFO_WAP_300x250_MTF_2', 'size' => '[300, 250]','isMobile'=>  true]
                ]
            ]) ?>
        <?php endif; ?>
    </div>
</div>

<?php if (!empty($forums)): ?>
    <?= $this->render('partials/_forum-card', [
        'forums' => $forums,
        'viewAllUrl' => $college->slug
    ]) ?>
<?php endif; ?>

<!-- gallery -->
<?php if (isset($menus['images-videos']) && !is_numeric($menus['images-videos']) && !empty($gallery)): ?>
    <div class="pageData photoGallery">
        <h2 class="row">Gallery <a href="<?= Url::toCollege($college->slug, 'images-videos') ?>">View All</a></h2>
        <div class="row">
            <?php foreach ($gallery as $image): ?>
                <div class="picture">
                    <img class="lazyload" width="273" height="206" loading="lazy" data-src="<?= Url::getCollegeImage($college->slug, $image->file) ?>" src="<?= Url::getCollegeImage($college->slug, $image->file) ?>" alt="">
                </div>
            <?php endforeach; ?>

            <?php if ($isMobile): ?>
                <div class="picture mobileOnly">
                    <div class="viewAllDiv">
                        <a href="<?= Url::toCollege($college->slug, 'images-videos') ?>"><i class="spriteIcon viewAllIcon"></i>VIEW ALL</a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<!-- contact us -->
<?php if (!empty($college->latitude) && !empty($college->longitude)): ?>
    <?= $this->render('partials/_contact-card', [
        'college' => $college,
    ]) ?>
<?php endif; ?>

<!-- Popular College Cards based on Stream + Level + State -->
<?php
$popularCollegeService = new PopularCollegeService();
$streamLocationColleges = $popularCollegeService->getCollegeCards(
    $college->primary_stream_id,
    null, // No specific degree for college page
    null  // State from IP via city relationship
);
if (!empty($streamLocationColleges)):
    ?>
    <?= $this->render('../layouts/_stream_location_college_cards', [
        'colleges' => $streamLocationColleges,
        'title' => 'Popular ' . (!empty($streamLocationColleges[0]['stream_name']) ? $streamLocationColleges[0]['stream_name'] : 'Engineering') . ' Colleges Near You'
    ]) ?>
<?php endif; ?>

<!-- other colleges under university -->
<?php if (!empty($affiliatedCollege) && !empty($parentCollege)): ?>
    <?= $this->render('partials/_college-card', [
        'title' => 'Other Colleges under ' . (!empty($parentCollege['display_name']) ? $parentCollege['display_name'] : $parentCollege['name']),
        'colleges' => $affiliatedCollege,
        'city' => $city,
        'state' => $state
    ]) ?>
<?php endif; ?>

<?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
    <?= $this->render('partials/_similar-college-card', [
        'collegeByDiscipline' => $collegeByDiscipline['colleges'],
        'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
        'college' => $college
    ]) ?>
<?php endif; ?>

<?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU() && $isMobile): ?>
    <?=
    $this->render('partials/_sidebar-ads.php', [
        'ads' => [
            ['slot' => 'GMU_COLLEGE_INFO_WAP_300x250_MTF_2', 'size' => '[300, 250]', 'isMobile' =>  true]
        ]
    ])
    ?>
<?php endif; ?>

<!-- Nearby colleges -->
<?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
    <?= $this->render('partials/_college-card', [
        'title' => 'Explore Nearby Colleges',
        'colleges' => $nearByCollege,
        'viewAllUrl' => !empty($city) ? "{$city->slug}" : '',
        'city' => $city,
        'state' => $state
    ]) ?>
<?php endif; ?>

<!-- Popular Colleges -->
<?php if (!empty($popularCollege)): ?>
    <?= $this->render('partials/_college-card', [
        'title' => 'Popular Colleges',
        'colleges' => $popularCollege,
        'city' => $city,
        'state' => $state
    ])
    ?>
<?php endif; ?>

<!-- related article -->
<?php if (!empty($article)): ?>
    <?= $this->render('../partials/_productArticleCard', [
        'relatedArticles' => $article,
    ]); ?>
<?php endif; ?>

<!-- related News -->
<?php if (!empty($news)): ?>
    <?= $this->render('../partials/_productNewsCard', [
        'news' => $news,
    ]); ?>
<?php endif; ?>

<!-- advertisement -->
<?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
    <aside>
        <div class="horizontalRectangle">
            <div class="appendAdDiv" style="background:#EAEAEA;">
                <?php if ($isMobile): ?>
                    <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250') ?>
                <?php else: ?>
                    <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                <?php endif; ?>
            </div>
        </div>
    </aside>
<?php endif; ?>

<?php /*if (!empty($sponsorClientUrl->redirection_link)):?>
    <?= $this->render('partials/_apply-now', [
        'sponsorClientUrl' => $sponsorClientUrl,
        'college' => $college
    ]) ?>
<?php  endif;*/ ?>