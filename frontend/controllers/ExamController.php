<?php

namespace frontend\controllers;

use frontend\services\ExamService;
use frontend\services\ArticleService;
use frontend\services\RankPredictorService;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\Category;
use common\models\Course;
use common\models\Exam;
use common\models\ForumCategory;
use common\services\FaqService;
use common\services\ForumService;
use frontend\models\ExamSearch;
use common\models\ExamDate;
use common\models\LeadBucketTagging;
use common\services\v2\NewsService;
use common\services\CollegeService;
use common\services\CourseService;
use frontend\helpers\Ad;
use Yii;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use common\services\QnaCommonService;
use common\models\Qna;
use common\services\UserService;
use yii\caching\TagDependency;
use yii\db\Query;
use backend\controllers\ExamContent;
use common\models\Stream;
use common\services\PopularCollegeService;

class ExamController extends Controller
{
    protected $examService;
    protected $newsService;
    protected $articleService;
    protected $faqService;
    protected $forumService;
    protected $collegeService;
    protected $courseService;
    protected $qnaCommonService;
    public $pageType;
    public $entityType;

    public function __construct(
        $id,
        $module,
        ExamService $examService,
        ArticleService $articleService,
        FaqService $faqService,
        ForumService $forumService,
        NewsService $newsService,
        CollegeService $collegeService,
        CourseService $courseService,
        QnaCommonService $qnaCommonService,
        $config = []
    ) {
        $this->examService = $examService;
        $this->articleService = $articleService;
        $this->faqService = $faqService;
        $this->forumService = $forumService;
        $this->newsService = $newsService;
        $this->collegeService = $collegeService;
        $this->courseService = $courseService;
        $this->qnaCommonService = $qnaCommonService;
        parent::__construct($id, $module, $config);
    }

    public static $_examPercentileValidation = [
        '23' => [200, 801],
        '26' => [36, 360],
        '25' => [3, 60],
        '33' => [30, 120],
        '93' => [100, 200],


    ];

    public function actionHome()
    {
        $response = DataHelper::trackStudentActivity('exam-home');
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];
        $langCode = array_search(DataHelper::getLangId(), DataHelper::$languageCode);

        return $this->render('index', [
            'filterPageInfo' => $this->examService->getFilterPageInfo('landing_page'),
            'upcomingExams' => $this->examService->upcomingExams(null, 4),
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_EXAM, 'landing'),
            'articles' => [], //$this->articleService->getAll('updated_at', 'desc'),
            'langCode' => $langCode,
        ]);
    }

    public function actionDetail($exam, $page = 'overview', $subject = '')
    {
        // Checking for main Syllabus page or a drop down Syllabus page
        $checkExam = '';

        $url = Yii::$app->request->url;
        $slug = basename($url); // Extracts the last part of the URL

        if (preg_match('/^(.*)-(exam-pattern|answer-key|syllabus|cut-off-(\d+))$/', $slug, $matches)) {
            $examSlug = $matches[1];
            $page = strpos($matches[2], 'cut-off') !== false ? 'cut-off' : $matches[2];

            $checkExam = $this->examService->getDetail($examSlug);
            if (!empty($checkExam)) {
                $exam = $checkExam->slug;
                if ($page === 'cut-off') {
                    $cutOffYear = $matches[3] ?? null;
                }
            }
        }

        $exam = $this->examService->getDetail($exam);
        if (empty($exam)) {
            throw new NotFoundHttpException();
        }

        $query = new Query;
        $parentPage = $query->select(['e.slug as parent_slug', 'ee.id', 'ee.slug'])
            ->from('exam_content as e')
            ->innerJoin('exam_content as ee', 'ee.parent_id = e.id')
            ->where(['ee.slug' => $page, 'e.exam_id' => $exam['id'], 'e.status' => Exam::STATUS_ACTIVE, 'ee.status' => Exam::STATUS_ACTIVE])
            ->one();

        if (!empty($subject) && empty($checkExam) && $page == 'syllabus') {
            return $this->dropDownPage($exam, $page, $subject, 'syllabus');
        } else if (($page == 'answer-key') && empty($checkExam)) {
            return $this->dropDownPage($exam, $page, $subject, 'answer-key');
        } else if (($page == 'exam-pattern') && empty($checkExam)) {
            return $this->dropDownPage($exam, $page, $subject, 'exam-pattern');
        } else if (($page == 'cut-off') && !empty($checkExam)) {
            return $this->dropDownPage($exam, $page, $cutOffYear, 'cut-off');
        } else if (!empty($parentPage['slug'])) {
            return $this->dropDownPage($exam, $parentPage['parent_slug'], $page, '');
        }

        if ($page == 'admit-card' || $page == 'form') {
            $response = DataHelper::trackStudentActivity('exam-detail', $page);
            $this->entityType = $response['entityType'];
            $this->pageType = $response['pageType'];
        } elseif ($page == 'results') {
            $this->pageType = 'result';
        } else {
            $this->pageType = $page;
        }
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'exam';
        $funArguments['currentPage'] = $page;
        $key = $this->getHash($funArguments);

        if ($data = $this->examService->getAdTargetData($exam)) {
            Ad::setTarget($data);
        }
        // yii::$app->cache->flush();
        // Yii::$app->cache->delete($key);
        // $data = Yii::$app->cache->getOrSet(DataHelper::generateCacheKey(), function () use ($exam, $page) {
        $dropDownPageValue = null;
        $content = $this->examService->getContent($exam['id'], $page);
        if (empty($content)) {
            throw new NotFoundHttpException();
        }

        $entitySubTypeId = $this->qnaCommonService->getEntitySubTypeId($page, 'exam');
        $qna = $this->qnaCommonService->getForumQna($exam->id, Qna::ENTITY_EXAM, $entitySubTypeId, 'qna_card');

        $examSubPageDropdown = $this->examService->getSubPageDropdown($exam, $page);
        // echo "<pre>"; print_r($examSubPageDropdown); die;
        $streams = [];
        $dates = $this->examService->getDate($exam['id']);
        $primaryStream = $exam->primaryStream;

        $upcomingExamByStream = isset($primaryStream->id) ? $this->examService->upcomingExams($primaryStream->id) : [];
        if (count($exam->streams)) {
            $streams = $exam->streams;
        }

        $popularExams = $this->examService->popularExams($exam['id']);
        $popularExamsIds = array_column($popularExams, 'id');
        $notIn = array_merge($popularExamsIds, array_column($upcomingExamByStream, 'id'));
        $streamId = $exam->primary_stream_id ?? '';

        $active_trans_data = [];
        $hindi_sub_pages = $this->examService->checkHindSubpage($exam['slug'], $page, $exam['lang_code']);
        if (!empty($hindi_sub_pages)) {
            foreach ($exam->activeTranslation as $trans_data) {
                $active_trans['cu_lang'] = DataHelper::getLangCode($exam['lang_code']);
                $active_trans['slug'] = $trans_data['slug'];
                $active_trans['page'] = $page;
                $active_trans['lang'] = DataHelper::getLangCode($trans_data['lang_code']);
                $active_trans_data[] = $active_trans;
            }
        }
        if (!empty($content->user->id)) {
            $getAuthorTrans = $this->examService->authorTranslation($content->user->id, $exam['lang_code']);
            if (!empty($getAuthorTrans)) {
                $content->user->name = $getAuthorTrans->user_name;
            }
        }
        if ($exam['lang_code'] != 1) {
            $get_en_name = Exam::find()->select(['display_name'])->where(['slug' => $exam['slug']])->andWhere(['lang_code' => 1])->one();
            $cta_display_name = $get_en_name['display_name'] ?? 'Exam';
        } else {
            $cta_display_name = $exam->display_name ?? $exam->name;
        }

        $downloadableResource = $this->examService->getDownloadableResource($exam->id, $exam->primary_stream_id, 'exam');

        $data = [
            'exam' => $exam,
            'dates' => $dates,
            'pageName' => $page,
            'streams' => $streams,
            'content' => $content,
            'updatebyAuth' => $this->examService->getUser($content->updated_by),
            'popularExams' => $popularExams,
            'primaryStream' => $primaryStream,
            'sponsorClientUrl' => DataHelper::getRedirectionLink('exam', $exam->id),
            'upcomingExamByStream' => $upcomingExamByStream,
            'pages' => $this->examService->getPages($exam->id),
            'upcomingExams' => $this->examService->upcomingExams(null, 10, [], ExamDate::TYPE_OFFICIAL),
            'pdfs' => $this->examService->getPagePdfs($exam->id, $page),
            'usefulLinks' => $this->examService->usefulLinks($exam, $page),
            'pageSeoInfo' => $this->examService->pageSeoInfo($exam['id'], $page),
            'intrestedExams' => $this->examService->interestedExams($streamId, '', 8),
            'pageContent' => ContentHelper::parseExamContent($content['content'], $dates, $content->localize_year ?? ContentHelper::EXAM_YEAR),
            'liveApplicationForm' => $this->examService->getLiveApplicationFormData($exam),
            'collegeAcceptingExams' => $this->examService->getCollegeAcceptingExams($exam['id'], $exam['slug']),
            'faqs' => $this->faqService->getPageLevelFaqDetails(Exam::ENTITY_EXAM, $exam->id, $page, ''),
            'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_EXAM_TYPE, $exam->slug, $page),
            'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
            'recentNews' => $this->newsService->getRecent(10),
            'trendingArticles' => $this->articleService->getTrendings(10),
            'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
            'recentActivity' => [$this->collegeService->getRecentActivityByEntity(Exam::ENTITY_EXAM, $exam->id, $page), (!empty($exam->display_name) ? $exam->display_name : $exam->name)],
            'forumQna' => $qna,
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_EXAM, $page, ($cta_display_name), $exam->slug),
            'dropdowns' => $examSubPageDropdown[0],
            'type' => $examSubPageDropdown[1],
            'translation_data' => $active_trans_data,
            'features' => $this->courseService->getFeature($exam->id, 'exam', array_keys(DataHelper::$featurArr)),
            'menuOrder' => $this->examService->getMenuOrder($exam->id, 'exam_content'),
            'downloadableResource' => $downloadableResource ?? [],
            'articleSubPage' => $this->examService->getArticleTopicPage($exam->slug, $page),
            'checkExamCollegePredictorMap' => $this->examService->checkExamCollegePredictorMap($exam->id),
            'popularColleges' => PopularCollegeService::getCollegeCards($exam->primary_stream_id, $exam->level, $exam->state_id, 5),
        ];
        //}, 60 * 60 * 3, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('detail', $data);
    }

    public function actionTest()
    {
        $searchModel = new ExamSearch();

        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionFilter($discipline, $location = 'india')
    {
        $response = DataHelper::trackStudentActivity('exam-filter');
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];
        $request = Yii::$app->request;
        $filterPageInfo = $this->examService->getFilterPageInfo($discipline);
        $stream = Stream::find()->select(['id'])->where(['slug' => $discipline])->one();
        $searchModel = new ExamSearch();

        $dataProvider = $searchModel->search($request->queryParams, $request->bodyParams);

        if ($dataProvider->totalCount == 0) {
            throw new NotFoundHttpException();
        }

        return $this->render('filter-new', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'filterPageInfo' => $filterPageInfo,
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_EXAM, 'category'),
            'stream' => !empty($stream) ? $stream : '',
        ]);
    }



    public function actionDiscipline($discipline, $location = 'india')
    {
        $a = '1';
        $params = [];
        $request = Yii::$app->request;
        $examSearch = new ExamSearch();

        $dataProvider = $examSearch->search($request->queryParams, $request->bodyParams);


        $params['stream'] = $discipline;
        $exams = $this->examService->filter($params);

        return $this->render('filter', [
            'exams' => $exams,
            'courses' => Course::find()->all(),
            'dataProvider' => $dataProvider,
            'searchModel' => $examSearch,
            'isAjax' => $request->isAjax ? 'hai' : 'na hai'
        ]);
    }

    public function actionRankPredict()
    {
        $request = Yii::$app->request->post();
        Yii::$app->response->format = Response::FORMAT_JSON;

        $examId = $request['examId'] ?? '';
        $marks = $request['marks'] ?? '';

        if ((empty($marks) || !(is_numeric($marks))) && $request['rankType'] == 'rank') {
            return 'Entered marks must be between 1 and 350.';
        }

        if ($request['rankType'] == 'percentile') {
            $examValidation = self::$_examPercentileValidation[$examId];
            if ($marks < $examValidation[0] || $marks > $examValidation[1]) {
                return 'Please enter score between ' . $examValidation[0] . ' and' . $examValidation[1];
            }
        }

        $result = new RankPredictorService();

        return $result->predictRank($examId, $marks);
    }

    /**
     * The below function is used for geting the details of qna landing page
     */
    public function actionQnaLandingPage($slug)
    {
        $total_qn_ans = $this->qnaCommonService->getTotalQuestionsAnswers();
        $qnaDetails = $this->qnaCommonService->getQnaDetails($slug, 'exam');
        // $qnaDetails = $this->qnaCommonService->getSubpageByEntity('exam', $pageName, $qnaDetails);
        $qnaLandingEntityDetails = $this->qnaCommonService->getEntityQnaLandingPage($slug, 'exam');

        return $this->render('../qna/_qnaLandingPageNew', [
            'qnadetails' => $qnaDetails,
            'qnaDetailsLatest' => $qnaLandingEntityDetails,
            'filterData' => QnaCommonService::$filterData,
            'total_qn_ans' => $total_qn_ans,
            'displayName' => isset($qnaDetails->exam->display_name) ? $qnaDetails->exam->display_name : $qnaDetails[0]->exam->display_name
        ]);
    }

    private function dropDownPage($exam, $parentPage, $page, $isSyllabus = null)
    {
        $parentPageSlug = (!empty($isSyllabus)) ? $isSyllabus : $parentPage;

        if ($page == 'admit-card' || $page == 'form') {
            $response = DataHelper::trackStudentActivity('exam-detail', $page);
            $this->entityType = $response['entityType'];
            $this->pageType = $response['pageType'];
        } elseif ($page == 'results') {
            $this->pageType = 'result';
        } else {
            $this->pageType = $page;
        }
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'exam';
        $funArguments['currentPage'] = $page;
        $key = $this->getHash($funArguments);
        $exam = $this->examService->getDetail($exam->slug);
        if (empty($exam)) {
            throw new NotFoundHttpException();
        }

        if ($data = $this->examService->getAdTargetData($exam)) {
            Ad::setTarget($data);
        }
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($exam, $page, $parentPage, $parentPageSlug) {


            $getParentId = $this->examService->getParentId($exam['id'], $parentPage);

            $content = $this->examService->getContent($exam['id'], $page, $getParentId);

            if (empty($content)) {
                throw new NotFoundHttpException();
            }

            $examSubPageDropdown = $this->examService->getSubPageDropdown($exam, $page);

            $streams = [];
            $dates = $this->examService->getDate($exam['id']);
            $primaryStream = $exam->primaryStream;

            $upcomingExamByStream = isset($primaryStream->id) ? $this->examService->upcomingExams($primaryStream->id) : [];
            if (count($exam->streams)) {
                $streams = $exam->streams;
            }

            $popularExams = $this->examService->popularExams($exam['id']);
            $popularExamsIds = array_column($popularExams, 'id');
            $notIn = array_merge($popularExamsIds, array_column($upcomingExamByStream, 'id'));
            $streamId = $exam->primary_stream_id ?? '';
            $downloadableResource = $this->examService->getDownloadableResource($exam->id, $exam->primary_stream_id, 'exam');

            return [
                'exam' => $exam,
                'dates' => $dates,
                'pageName' => $page,
                'streams' => $streams,
                'content' => $content,
                'popularExams' => $popularExams,
                'primaryStream' => $primaryStream,
                'sponsorClientUrl' => DataHelper::getRedirectionLink('exam', $exam->id),
                'upcomingExamByStream' => $upcomingExamByStream,
                'pages' => $this->examService->getPages($exam->id),
                'upcomingExams' => $this->examService->upcomingExams(null, 10, [], ExamDate::TYPE_OFFICIAL),
                'pdfs' => $this->examService->getPagePdfs($exam->id, $parentPage),
                'usefulLinks' => $this->examService->usefulLinks($exam, $page),
                'pageSeoInfo' => $this->examService->pageSeoInfo($exam['id'], $page, $getParentId),
                'intrestedExams' => $this->examService->interestedExams($streamId, '', 8),
                'pageContent' => ContentHelper::parseExamContent($content->content, $dates, $content->localize_year ?? ContentHelper::EXAM_YEAR),
                'liveApplicationForm' => $this->examService->getLiveApplicationFormData($exam),
                'collegeAcceptingExams' => $this->examService->getCollegeAcceptingExams($exam['id']),
                'faqs' => $this->faqService->getPageLevelFaqDetails(Exam::ENTITY_EXAM, $exam->id, $parentPageSlug, $page),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_EXAM_TYPE, $exam->slug, $parentPage),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_EXAM, $parentPage, ($exam->display_name ?? $exam->name), $exam->slug),
                // 'recentActivity' => [$this->collegeService->getRecentActivityByEntity(Exam::ENTITY_EXAM, $exam->id, $page), (!empty($exam->display_name) ? $exam->display_name : $exam->name)],
                'dropdowns' => $examSubPageDropdown[0],
                'type' => $examSubPageDropdown[1],
                'menuOrder' => $this->examService->getMenuOrder($exam->id, 'exam_content'),
                'downloadableResource' => $downloadableResource ?? [],
                'articleSubPage' => $this->examService->getArticleTopicPage($exam->slug, $page),
                'parentPage' => $parentPage,
                'popularColleges' => PopularCollegeService::getCollegeCards($exam->primary_stream_id, $exam->level, $exam->state_id, 4),
            ];
        }, 60 * 60 * 3, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('detail', $data);
    }
}
