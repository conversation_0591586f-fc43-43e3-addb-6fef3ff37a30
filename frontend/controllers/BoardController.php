<?php

namespace frontend\controllers;

use common\helpers\DataHelper;
use common\helpers\BoardHelper;
use common\services\UserService;
use common\models\Board;
use common\models\Category;
use common\models\LeadBucketTagging;
use yii\web\NotFoundHttpException;
use common\services\BoardService;
use frontend\services\ExamService;
use common\services\v2\NewsService;
use frontend\services\ArticleService;
use common\services\FaqService;
use common\services\CollegeService;
use frontend\helpers\Ad;
use frontend\models\CommentForm;
use common\models\Qna;
use common\services\QnaCommonService;
use common\models\UserTranslation;
use common\models\BoardPages;
use common\services\PopularCollegeService;
use Yii;

class BoardController extends Controller
{
    protected $boardService;
    protected $examService;
    protected $articleService;
    protected $newsService;
    protected $faqService;
    protected $collegeService;
    protected $qnaCommonService;
    public $pageType;
    public $entityType;

    public function __construct(
        $id,
        $module,
        BoardService $boardService,
        ExamService $examService,
        ArticleService $articleService,
        NewsService $newsService,
        FaqService $faqService,
        CollegeService $collegeService,
        QnaCommonService $qnaCommonService,
        $config = []
    ) {

        $this->boardService = $boardService;
        $this->examService = $examService;
        $this->articleService = $articleService;
        $this->newsService = $newsService;
        $this->faqService = $faqService;
        $this->collegeService = $collegeService;
        $this->qnaCommonService = $qnaCommonService;

        parent::__construct($id, $module, $config);
    }

    public function actionIndex()
    {
        $response = DataHelper::trackStudentActivity('board-index');
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];
        return $this->render('index', [
            'boardList' => $this->boardService->getBoardList(),
            'articles' => $this->articleService->getByCategory(17),
            'boardExams' => $this->newsService->getCategoryNews(7),
            'homePageContent' => $this->boardService->getHomePageContent(),
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_BOARDS, 'landing'),
        ]);
    }

    public function actionDetail($board, $page = 'overview', $subject = '')
    {
        $urlPattern = $this->boardService->correctUrl($board, $page);

        $board = $urlPattern['board'];
        $page = $urlPattern['page'];

        if (!empty($subject)) {
            return $this->dropDownPage($board, $subject, $page);
        }

        $response = DataHelper::trackStudentActivity('board-detail', $page);
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];
        $board = $this->boardService->getDetail($board);

        if (empty($board)) {
            throw new NotFoundHttpException();
        }

        if ($data = $this->boardService->getAdTargetData($board)) {
            Ad::setTarget($data);
        }

        $content = $this->boardService->getContent($board->id, $page);

        if (empty($content)) {
            throw new NotFoundHttpException();
        }
        $boardSubPageDropdown = $this->boardService->getSubPageDropdown($board, $page);
        $exam = $this->boardService->getExam($board->id);
        $subject = $this->boardService->getBoardSubject($content->id);
        $active_trans_data = [];

        $hindi_sub_pages = $this->boardService->checkHindSubpage($board->slug, $page, $board->lang_code);
        if (!empty($hindi_sub_pages)) {
            foreach ($board->activeTranslation as $trans_data) {
                $active_trans['cu_lang'] = DataHelper::getLangCode($board->lang_code);
                $active_trans['slug'] = $trans_data['slug'];
                $active_trans['page'] = $page;
                $active_trans['lang'] = DataHelper::getLangCode($trans_data['lang_code']);
                $active_trans_data[] = $active_trans;
            }
        }

        if (isset($content->author) && !empty($content->author)) {
            $getAuthorTrans = $this->authorTranslation($content->author->id, $board->lang_code);
            if (!empty($getAuthorTrans)) {
                $content->author->name = $getAuthorTrans->user_name;
            }
        }

        $entitySubTypeId = $this->qnaCommonService->getEntitySubTypeId($page, 'board');
        $qna = $this->qnaCommonService->getForumQna($board->id, Qna::ENTITY_BOARD, $entitySubTypeId, 'qna_card');

        $state = null;
        if (isset($board->state_id) && !empty($board->state_id)) {
            $state = $this->boardService->getStateName($board->state_id);
        }
        // get Default Meta
        $defaultmetaContent = $this->boardService->getBoardDefaultSeoInfo($board, $page, '', false);

        return $this->render('detail', [
            'board' => $board,
            'pageName' => $page,
            'exams' => $exam,
            'subject' => $subject,
            'sponsorClientUrl' => DataHelper::getRedirectionLink('board', $board->id),
            'pageContent' => $content,
            'content' => BoardHelper::parseBoardContent($content->content),
            'pages' => $this->boardService->getPages($board->id),
            'faqs' => $this->faqService->getPageLevelFaqDetails(Board::ENTITY_BOARD, $board->id, $page),
            // 'articles' => $this->articleService->getByCategory(17),
            // 'boardExams' => $this->newsService->getCategoryNews(7),
            'stateColleges' => $this->boardService->getCollegesByState($board->state_id),
            'streamColleges' => $this->boardService->getStreamBasedStateList($board->state_id),
            //  'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
            // 'recentNews' => $this->newsService->getRecent(10),
            //   'trendingArticles' => $this->articleService->getTrendings(10),
            'translation_data' => $active_trans_data,
            //  'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_BOARDS, $page, ($board->display_name ?? $board->name), $board->slug),
            'recentActivity' => [$this->collegeService->getRecentActivityByEntity(Board::ENTITY_BOARD, $board->id, $page), (!empty($board->display_name) ? $board->display_name : $board->name)],
            'forumQna' => $qna,
            'dropdowns' => $boardSubPageDropdown,
            'type' => null,
            'state' => $state,
            'article' => $board->article,
            'news' => $board->news,
            'menuOrder' => $this->boardService->getMenuOrder($board->id, 'board_content'),
            'allBoardPages' =>   $this->boardService->getAllBoardPage(),
            'defaultmetaContent' => $defaultmetaContent ?? [],
            'popularColleges' => PopularCollegeService::getCollegeCards('', '', $board->state_id, 4),

        ]);
    }

    public function actionBoardDetail($board, $page = 'overview', $subject = '')
    {
        $boardSlug = (!empty(array_keys(BoardHelper::$redirectBoards, $board)[0])) ? array_keys(BoardHelper::$redirectBoards, $board)[0] : $board;
        return $this->actionDetail($boardSlug, $page, $subject);
    }

    public function authorTranslation($author_id, $lang_code)
    {
        $author_translation = UserTranslation::find()->select(['user_name'])
            ->where(['tag_user_id' => $author_id])
            ->andWhere(['lang_code' => $lang_code])
            ->one();
        return $author_translation;
    }

    public function actionSamplePaper($boardSamplePaperSlug)
    {

        $samplePaper = $this->boardService->getBoardSamplePaper($boardSamplePaperSlug);

        if (!$samplePaper) {
            throw new NotFoundHttpException();
        }

        $board = $samplePaper->boardDetails->board;
        $exam = $this->boardService->getExam($board->id);
        $menus = $this->boardService->getMenu($board->id);
        $pageName = $samplePaper->boardDetails;

        $subjectSet = $this->boardService->getBoardSamplePaperSet($samplePaper->id);
        $defaultmetaContent = $this->boardService->getBoardDefaultSeoInfo($board, $pageName, '', false);
        return $this->render('board-sample-paper-detail', [
            'commentModel' => new CommentForm(),
            'subjectContent' => $samplePaper,
            'content' => BoardHelper::parseBoardContent($samplePaper->content),
            'board' => $board,
            'pages' => $menus,
            'exams' => $exam,
            'sponsorClientUrl' => DataHelper::getRedirectionLink('board', $board->id),
            'page' => $samplePaper->boardDetails->page,
            'pageName' => $pageName->page_slug,
            'pdfs' => $subjectSet,
            'comments' => $this->boardService->getComments($samplePaper->id),
            'streamColleges' => $this->boardService->getStreamBasedStateList($board->state_id),
            'articles' => $this->articleService->getByCategory(17),
            'boardExams' => $this->newsService->getCategoryNews(7),
            'stateColleges' => $this->boardService->getCollegesByState($board->state_id),
            'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
            'recentNews' => $this->newsService->getRecent(10),
            'trendingArticles' => $this->articleService->getTrendings(10),
            'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_BOARDS, $pageName->page_slug, $board->display_name ?? $board->name, $board->slug),
            'menuOrder' => $this->boardService->getMenuOrder($board->id, 'board_content'),
            'allBoardPages' =>   $this->boardService->getAllBoardPage(),
            'defaultmetaContent' => $defaultmetaContent ?? []
        ]);
    }

    public function actionQnaLandingPage($slug)
    {
        $total_qn_ans = $this->qnaCommonService->getTotalQuestionsAnswers();
        $qnaDetails = $this->qnaCommonService->getQnaDetails($slug, 'board');
        $qnaLandingEntityDetails = $this->qnaCommonService->getEntityQnaLandingPage($slug, 'board');
        // $qnaDetails = $this->qnaCommonService->getSubpageByEntity('board', $pageName, $qnaDetails);
        $firstQna = reset($qnaDetails);
        return $this->render('../qna/_qnaLandingPageNew', [
            'qnadetails' => $qnaDetails,
            'qnaDetailsLatest' => $qnaLandingEntityDetails,
            'filterData' => QnaCommonService::$filterData,
            'total_qn_ans' => $total_qn_ans,
            'displayName' => array_key_exists('entity', $firstQna) ? $firstQna['display_name'] : $firstQna->board->display_name
        ]);
    }
    protected function dropDownPage($board, $page, $parentPage)
    {

        // if (str_contains($page, '-smy')) {
        //     $parentPage = 'supplementary';
        //     $type = $parentPage;
        //     $page = str_replace('-smy', '', $page);
        // } else {
        $type = $parentPage;
        // }
        $response = DataHelper::trackStudentActivity('board-detail', $page, 'drop-down');
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];
        $board = $this->boardService->getDetail($board);

        if (empty($board)) {
            throw new NotFoundHttpException();
        }

        if ($data = $this->boardService->getAdTargetData($board)) {
            Ad::setTarget($data);
        }

        $content = $this->boardService->getSubPageContent($board->id, $page, $parentPage);
        if (empty($content)) {
            throw new NotFoundHttpException();
        }

        $boardSubPageDropdown = $this->boardService->getSubPageDropdown($board, $parentPage);
        $exam = $this->boardService->getExam($board->id);

        $prentContent = $this->boardService->getContent($board->id, $parentPage);
        $subject = !empty($prentContent->id) ? $this->boardService->getBoardSubject($prentContent->id) : [];

        $active_trans_data = [];

        $hindi_sub_pages = $this->boardService->checkHindSubpage($board->slug, $page, $board->lang_code);
        if (!empty($hindi_sub_pages)) {
            foreach ($board->activeTranslation as $trans_data) {
                $active_trans['cu_lang'] = DataHelper::getLangCode($board->lang_code);
                $active_trans['slug'] = $trans_data['slug'];
                $active_trans['lang'] = DataHelper::getLangCode($trans_data['lang_code']);
                $active_trans_data[] = $active_trans;
            }
        }
        if (isset($content->author) && !empty($content->author)) {
            $getAuthorTrans = $this->authorTranslation($content->author->id, $board->lang_code);
            if (!empty($getAuthorTrans)) {
                $content->author->name = $getAuthorTrans->user_name;
            }
        }
        $state = null;
        if (isset($board->state_id) && !empty($board->state_id)) {
            $state = $this->boardService->getStateName($board->state_id);
        }
        $defaultmetaContent = $this->boardService->getBoardDefaultSeoInfo($board, $parentPage, $page, true);
        return $this->render('detail', [
            'board' => $board,
            'pageName' => $page,
            'exams' => $exam,
            'subject' => $subject,
            'sponsorClientUrl' => DataHelper::getRedirectionLink('board', $board->id),
            'pageContent' => $content,
            'content' => BoardHelper::parseBoardContent($content->content),
            'pages' => $this->boardService->getPages($board->id),
            'faqs' => $this->faqService->getPageLevelFaqDetails(Board::ENTITY_BOARD, $board->id, $parentPage, $page),
            'streamColleges' => $this->boardService->getStreamBasedStateList($board->state_id),
            // 'articles' => $this->articleService->getByCategory(17),
            // 'boardExams' => $this->newsService->getCategoryNews(7),
            'stateColleges' => $this->boardService->getCollegesByState($board->state_id),
            // 'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
            //  'recentNews' => $this->newsService->getRecent(10),
            // 'trendingArticles' => $this->articleService->getTrendings(10),
            'translation_data' => $active_trans_data,
            'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_BOARDS, $parentPage, ($board->display_name ?? $board->name), $board->slug),
            // 'recentActivity' => [$this->collegeService->getRecentActivityByEntity(Board::ENTITY_BOARD, $board->id, $parentPage), (!empty($board->display_name) ? $board->display_name : $board->name)],
            'dropdowns' => $boardSubPageDropdown,
            'type' => $type,
            'state' => $state,
            'article' => $board->article,
            'news' => $board->news,
            'menuOrder' => $this->boardService->getMenuOrder($board->id, 'board_content'),
            'allBoardPages' =>   $this->boardService->getAllBoardPage(),
            'defaultmetaContent' => $defaultmetaContent ?? []
        ]);
    }
}
