<?php

use yii\helpers\Html;
use yii\grid\GridView;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\web\JsExpression;
use common\models\College;
use common\models\PopularCollege;

/* @var $this yii\web\View */
/* @var $college common\models\College */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Manage College Tags by College';
$this->params['breadcrumbs'][] = ['label' => 'Popular Colleges', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

// Register CSS for enhanced styling
$this->registerCss('
.college-selector {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}
.college-info-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
}
.tag-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}
.tag-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}
.tag-summary {
    background: #e8f5e8;
    border: 1px solid #c3e6c3;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}
.quick-actions {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}
');

// Register JavaScript
$this->registerJs("
function loadCollegeTags(collegeId) {
    if (collegeId) {
        window.location.href = '" . \yii\helpers\Url::to(['manage-by-college']) . "?college_id=' + collegeId;
    }
}

function addQuickTag(collegeId, streamId, degreeId, stateId) {
    $.post('" . \yii\helpers\Url::to(['quick-add-tag']) . "', {
        college_id: collegeId,
        stream_id: streamId,
        degree_id: degreeId,
        state_id: stateId
    }, function(response) {
        if (response.success) {
            location.reload();
        } else {
            alert('Error: ' + response.message);
        }
    });
}
");
?>

<div class="manage-by-college">
    <!-- College Selector -->
    <div class="college-selector">
        <h3><i class="fa fa-university"></i> Select College to Manage Tags</h3>
        <div class="row">
            <div class="col-md-8">
                <?= Select2::widget([
                    'name' => 'college_selector',
                    'value' => $college ? $college->id : '',
                    'data' => $college ? [$college->id => $college->name] : [],
                    'options' => [
                        'placeholder' => 'Search and select a college...',
                        'id' => 'college-selector',
                        'onchange' => 'loadCollegeTags(this.value)'
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 3,
                        'ajax' => [
                            'url' => ['../ajax/college-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ]) ?>
            </div>
            <div class="col-md-4">
                <?= Html::a('<i class="fa fa-plus"></i> Add New Tag', ['create'], [
                    'class' => 'btn btn-success btn-flat btn-lg'
                ]) ?>
            </div>
        </div>
    </div>

    <?php if ($college): ?>
        <!-- College Information -->
        <div class="college-info-card">
            <div class="row">
                <div class="col-md-8">
                    <h3><?= Html::encode($college->name) ?></h3>
                    <p class="text-muted">
                        <i class="fa fa-map-marker"></i> 
                        <?= $college->city ? $college->city->name : 'N/A' ?>, 
                        <?= $college->city && $college->city->state ? $college->city->state->name : 'N/A' ?>
                    </p>
                    <?php if ($college->primary_stream_id): ?>
                        <p><strong>Primary Stream:</strong> 
                            <span class="label label-primary"><?= $college->primaryStream ? $college->primaryStream->name : 'N/A' ?></span>
                        </p>
                    <?php endif; ?>
                </div>
                <div class="col-md-4 text-right">
                    <div class="tag-summary">
                        <h4><?= $dataProvider->getTotalCount() ?></h4>
                        <small>Total Tags</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h4><i class="fa fa-bolt"></i> Quick Add Common Tags</h4>
            <p class="text-muted">Add popular stream + level combinations for this college</p>
            <div class="row">
                <div class="col-md-3">
                    <button class="btn btn-info btn-block" onclick="addQuickTag(<?= $college->id ?>, 11, 1, <?= $college->city ? $college->city->state_id : 1 ?>)">
                        Engineering + Bachelor
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info btn-block" onclick="addQuickTag(<?= $college->id ?>, 12, 1, <?= $college->city ? $college->city->state_id : 1 ?>)">
                        Medical + Bachelor
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info btn-block" onclick="addQuickTag(<?= $college->id ?>, 13, 1, <?= $college->city ? $college->city->state_id : 1 ?>)">
                        Management + Bachelor
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info btn-block" onclick="addQuickTag(<?= $college->id ?>, 11, 2, <?= $college->city ? $college->city->state_id : 1 ?>)">
                        Engineering + Master
                    </button>
                </div>
            </div>
        </div>

        <!-- Existing Tags -->
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-tags"></i> Existing Tags for <?= Html::encode($college->name) ?></h3>
                <div class="box-tools pull-right">
                    <?= Html::a('<i class="fa fa-plus"></i> Add New Tag', ['create', 'college_id' => $college->id], [
                        'class' => 'btn btn-success btn-sm btn-flat'
                    ]) ?>
                </div>
            </div>
            <div class="box-body">
                <?php if ($dataProvider->getTotalCount() > 0): ?>
                    <?= GridView::widget([
                        'dataProvider' => $dataProvider,
                        'layout' => "{items}\n{summary}\n{pager}",
                        'tableOptions' => ['class' => 'table table-striped table-hover'],
                        'columns' => [
                            ['class' => 'yii\grid\SerialColumn'],
                            
                            [
                                'attribute' => 'stream_id',
                                'label' => 'Stream',
                                'value' => function ($model) {
                                    return $model->stream ?
                                        '<span class="label label-primary">' . $model->stream->name . '</span>' :
                                        '<span class="label label-default">N/A</span>';
                                },
                                'format' => 'html'
                            ],
                            [
                                'attribute' => 'degree_id',
                                'label' => 'Level',
                                'value' => function ($model) {
                                    return $model->degree ?
                                        '<span class="label label-info">' . $model->degree->name . '</span>' :
                                        '<span class="label label-default">N/A</span>';
                                },
                                'format' => 'html'
                            ],
                            [
                                'attribute' => 'state_id',
                                'label' => 'State',
                                'value' => function ($model) {
                                    return $model->state ?
                                        '<span class="label label-success">' . $model->state->name . '</span>' :
                                        '<span class="label label-default">N/A</span>';
                                },
                                'format' => 'html'
                            ],
                            [
                                'attribute' => 'city_id',
                                'label' => 'City',
                                'value' => function ($model) {
                                    return $model->city ? $model->city->name : 'All Cities';
                                },
                                'format' => 'text'
                            ],
                            [
                                'attribute' => 'position',
                                'label' => 'Priority',
                                'value' => function ($model) {
                                    return '<span class="badge bg-blue">' . $model->position . '</span>';
                                },
                                'format' => 'html',
                                'contentOptions' => ['style' => 'text-align: center;']
                            ],
                            [
                                'attribute' => 'status',
                                'label' => 'Status',
                                'value' => function ($model) {
                                    return $model->status == 1 ?
                                        '<span class="label label-success">Active</span>' :
                                        '<span class="label label-danger">Inactive</span>';
                                },
                                'format' => 'html',
                                'contentOptions' => ['style' => 'text-align: center;']
                            ],
                            [
                                'class' => 'yii\grid\ActionColumn',
                                'template' => '{update} {delete} {duplicate}',
                                'buttons' => [
                                    'duplicate' => function ($url, $model, $key) {
                                        return Html::a('<i class="fa fa-copy"></i>', ['duplicate', 'id' => $model->id], [
                                            'title' => 'Duplicate',
                                            'class' => 'btn btn-xs btn-info'
                                        ]);
                                    },
                                    'update' => function ($url, $model, $key) {
                                        return Html::a('<i class="fa fa-edit"></i>', $url, [
                                            'title' => 'Update',
                                            'class' => 'btn btn-xs btn-warning'
                                        ]);
                                    },
                                    'delete' => function ($url, $model, $key) {
                                        return Html::a('<i class="fa fa-trash"></i>', $url, [
                                            'title' => 'Delete',
                                            'class' => 'btn btn-xs btn-danger',
                                            'data-confirm' => 'Are you sure you want to delete this tag?',
                                            'data-method' => 'post'
                                        ]);
                                    },
                                ],
                                'contentOptions' => ['style' => 'text-align: center; white-space: nowrap;']
                            ],
                        ],
                    ]); ?>
                <?php else: ?>
                    <div class="alert alert-info">
                        <h4><i class="fa fa-info-circle"></i> No Tags Found</h4>
                        <p>This college doesn't have any tags configured yet. Use the "Add New Tag" button or quick actions above to create tags.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <div class="alert alert-info">
            <h4><i class="fa fa-info-circle"></i> Select a College</h4>
            <p>Please select a college from the dropdown above to view and manage its tags.</p>
        </div>
    <?php endif; ?>
</div>
