<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model yii\base\DynamicModel */

$this->title = '';
$this->params['breadcrumbs'][] = ['label' => 'Popular Colleges', 'url' => ['index']];
?>
<style>
    .popular-college-bulk-upload .box-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .popular-college-bulk-upload .box-header h3 {
        color: white;
        margin: 6px;
        font-weight: 600;
    }

    .alert-info {
        background-color: #b0d3db !important;
    }

    .alert-info p,
    .alert-info li,
    .alert-info ul {
        color: black;
    }
</style>
<div class="popular-college-bulk-upload" style="margin-top: 20px;">
    <div class="box box-primary">
        <div class="box-header with-border">
            <h3 class="box-title">Bulk Upload Popular Colleges</h3>
            <?= Html::a(
                '<i class="fa fa-download"></i> Download Sample Data',
                ['/popular_college_bulk_upload_sample.csv'],
                ['class' => 'btn btn-default btn-flat pull-right']
            ) ?>
        </div>

        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <?php $form = ActiveForm::begin(['options' => ['enctype' => 'multipart/form-data']]); ?>

                    <div class="form-group">
                        <label>CSV File Format Instructions:</label>
                        <div class="alert alert-info">
                            <p><strong>Required CSV columns (in this exact order):</strong></p>
                            <ol>
                                <li><strong>college_id</strong></li>
                                <li><strong>stream_id</strong></li>
                                <li><strong>level_id</strong></li>
                                <li><strong>city_id</strong></li>
                                <li><strong>state_id</strong></li>
                            </ol>
                            <p><strong>Important Notes:</strong></p>
                            <ul>
                                <li><span class="text-danger"><strong>All values must be numeric IDs</strong></span></li>
                                <li>IDs must exist in their respective tables (college, stream, degree, city, state)</li>
                                <li><span class="text-danger"><strong>Maximum 100 records allowed per upload</strong></span></li>
                                <li>Files with more than 100 records will be rejected completely</li>
                                <li>Duplicate records will be skipped automatically</li>
                                <li>Status will be set to Active by default</li>
                            </ul>
                        </div>
                    </div>

                    <?= $form->field($model, 'csvFile')->fileInput([
                        'accept' => '.csv',
                        'class' => 'form-control'
                    ])->label('Select CSV File') ?>

                    <div class="form-group">
                        <?= Html::submitButton('Upload', ['class' => 'btn btn-success']) ?>
                        <?= Html::a('Cancel', ['index'], ['class' => 'btn btn-default']) ?>
                    </div>

                    <?php ActiveForm::end(); ?>
                </div>
            </div>
        </div>
    </div>
</div>