<?php

use common\models\PopularCollege;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\Json;
use yii\widgets\Pjax;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\PopularCollegeSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'College Tagging Management';

// Get statistics
$activeRecords = \common\models\PopularCollege::find()->where(['status' => PopularCollege::STATUS_ACTIVE])->count();
$inactiveRecords = \common\models\PopularCollege::find()->where(['status' => PopularCollege::STATUS_INACTIVE])->count();

$bulkActivateUrl   = Json::htmlEncode(\yii\helpers\Url::to(['bulk-activate']));
$bulkDeactivateUrl = Json::htmlEncode(\yii\helpers\Url::to(['bulk-deactivate']));
?>
<style>
    .content-header {
        display: none;
    }

    .popular-college-index .box-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .popular-college-index .box-header h3 {
        color: white;
        margin: 0;
        font-weight: 600;
    }

    .stats-card {
        background: white;
        border-radius: 8px;
        padding: 0px 10px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #667eea;
    }

    .stats-number {
        font-size: 2em;
        font-weight: bold;
        color: #667eea;
    }

    .stats-label {
        color: #666;
        font-size: 0.9em;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .action-buttons {
        margin-bottom: 20px;
    }

    .filter-section {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    .table-responsive {
        border-radius: 5px;
        overflow: hidden;
    }

    .grid-view th {
        background: #f1f3f4;
        font-weight: 600;
    }

    .status-active {
        color: #28a745;
        font-weight: bold;
    }

    .status-inactive {
        color: #dc3545;
        font-weight: bold;
    }

    .stats-card-row {
        width: 12%;
    }

    /* Loading indicator styles for auto-filtering */
    .loading-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 255, 255, 0.95);
        padding: 20px 30px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        font-size: 16px;
        color: #666;
        border: 1px solid #ddd;
    }

    .loading-overlay i {
        margin-right: 10px;
        font-size: 18px;
        color: #667eea;
    }

    #popular-college-grid.loading {
        position: relative;
        opacity: 0.6;
        pointer-events: none;
    }

    /* Auto-filter info styling */
    .auto-filter-info {
        background: #e8f5e8;
        border: 1px solid #c3e6c3;
        border-radius: 4px;
        padding: 8px 12px;
        margin-bottom: 15px;
        font-size: 13px;
        color: #155724;
    }
</style>

<div class="popular-college-index" style="margin-top: 20px;">
    <!-- Header Section -->
    <div class="box box-primary">
        <div class="box-header with-border">
            <h3><i class="fa fa-university"></i> College Tagging Management</h3>
            <p style="margin: 5px 0 0 0; opacity: 0.9;">Manage popular colleges for stream + level + location based display</p>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3 stats-card-row">
            <div class="stats-card">
                <div class="stats-number active"><?= $activeRecords ?></div>
                <div class="stats-label">Active Records</div>
            </div>
        </div>
        <div class="col-md-3 stats-card-row">
            <div class="stats-card">
                <div class="stats-number inactive"><?= $inactiveRecords ?></div>
                <div class="stats-label">Inactive Records</div>
            </div>
        </div>
        <div class="action-buttons">
            <div class="row">
                <div class="col-md-8">
                    <?= Html::a('<i class="fa fa-plus"></i> Add New', ['create'], [
                        'class' => 'btn btn-success btn-flat',
                        'style' => 'margin-right: 10px;'
                    ]) ?>
                    <?= Html::a('<i class="fa fa-upload"></i> Bulk Upload', ['bulk-upload'], [
                        'class' => 'btn btn-info btn-flat',
                        'style' => 'margin-right: 10px;'
                    ]) ?>
                    <button class="btn btn-success btn-flat" id="activate-btn">
                        <i class="fa fa-check"></i> Activate Selected
                    </button>
                    <button class="btn btn-warning btn-flat" id="deactivate-btn">
                        <i class="fa fa-times"></i> Deactivate Selected
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters Section -->
    <div id="advanced-filters" class="filter-section">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-filter"></i> Filters</h3>
            </div>
            <div class="box-body">
                <?php echo $this->render('_search', ['model' => $searchModel]); ?>
            </div>
        </div>
    </div>

    <!-- Data Grid -->
    <div class="box box-default">
        <div class="box-body table-responsive no-padding">
            <?php Pjax::begin(['id' => 'popular-college-grid']); ?>
            <?= GridView::widget([
                'dataProvider' => $dataProvider,
                'layout' => "{items}\n{summary}\n{pager}",
                'tableOptions' => ['class' => 'table table-striped table-hover'],
                'columns' => [
                    [
                        'class' => 'yii\grid\CheckboxColumn',
                        'headerOptions' => ['style' => 'width: 40px;']
                    ],
                    [
                        'class' => 'yii\grid\SerialColumn',
                        'headerOptions' => ['style' => 'width: 60px;']
                    ],

                    [
                        'attribute' => 'college_id',
                        'label' => 'College',
                        'value' => function ($model) {
                            $collegeName = $model->college ? $model->college->name : 'N/A';
                            $cityName = $model->college && $model->college->city ? $model->college->city->name : '';
                            return $collegeName . ($cityName ? " ({$cityName})" : '');
                        },
                        'format' => 'html',
                        'contentOptions' => ['style' => 'max-width: 250px; word-wrap: break-word;']
                    ],
                    [
                        'attribute' => 'stream_id',
                        'label' => 'Stream',
                        'value' => function ($model) {
                            return $model->stream ? $model->stream->name : 'N/A';
                        },
                        'format' => 'html',
                        'headerOptions' => ['style' => 'width: 120px;']
                    ],
                    [
                        'attribute' => 'degree_id',
                        'label' => 'Level',
                        'value' => function ($model) {
                            return $model->degree ? $model->degree->name : 'N/A';
                        },
                        'format' => 'html',
                        'headerOptions' => ['style' => 'width: 100px;']
                    ],
                    [
                        'attribute' => 'state_id',
                        'label' => 'State',
                        'value' => function ($model) {
                            return $model->state ? $model->state->name : 'N/A';
                        },
                        'format' => 'html',
                        'headerOptions' => ['style' => 'width: 120px;']
                    ],
                    [
                        'attribute' => 'city_id',
                        'label' => 'City',
                        'value' => function ($model) {
                            return $model->city ? $model->city->name : 'N/A';
                        },
                        'format' => 'text',
                        'headerOptions' => ['style' => 'width: 120px;']
                    ],
                    [
                        'attribute' => 'status',
                        'label' => 'Status',
                        'value' => function ($model) {
                            if ($model->status == 1) {
                                return '<span class="label label-success">Active</span>';
                            } else {
                                return '<span class="label label-danger">Inactive</span>';
                            }
                        },
                        'format' => 'html',
                        'headerOptions' => ['style' => 'width: 100px;'],
                    ],

                    [
                        'class' => 'yii\grid\ActionColumn',
                        'header' => 'Actions',
                        'template' => '{view} {update}',
                        'buttons' => [
                            'view' => function ($url, $model, $key) {
                                return Html::a('<i class="fa fa-eye"></i>', $url, [
                                    'title' => 'View',
                                    'class' => 'btn btn-xs btn-primary',
                                    'data-pjax' => '0'
                                ]);
                            },
                            'update' => function ($url, $model, $key) {
                                return Html::a('<i class="fa fa-edit"></i>', $url, [
                                    'title' => 'Update',
                                    'class' => 'btn btn-xs btn-warning',
                                    'data-pjax' => '0'
                                ]);
                            },
                        ],
                        'headerOptions' => ['style' => 'width: 140px; text-align: center;'],
                        'contentOptions' => ['style' => 'text-align: center; white-space: nowrap;']
                    ],
                ],
            ]); ?>
            <?php Pjax::end(); ?>
        </div>
    </div>
</div>

<?php
$script = <<<JS
function getSelectedIds() {
    var selected = [];
    $('input[name="selection[]"]:checked').each(function() {
        selected.push($(this).val());
    });
    return selected;
}


// Bulk activate
$('#activate-btn').on('click', function() {
    var ids = getSelectedIds();
    if (ids.length === 0) {
        alert('Please select items to activate');
        return;
    }
    if (confirm('Activate ' + ids.length + ' selected items?')) {
        $.post($bulkActivateUrl, {ids: ids}, function(data) {
            if (data.success) {
                $.pjax.reload({container: '#popular-college-grid'});
                $('.stats-number.active').text(data.active);
                $('.stats-number.inactive').text(data.inactive);
            }
        });
    }
});

// Bulk deactivate
$('#deactivate-btn').on('click', function() {
    var ids = getSelectedIds();
    if (ids.length === 0) {
        alert('Please select items to deactivate');
        return;
    }
    if (confirm('Deactivate ' + ids.length + ' selected items?')) {
        $.post($bulkDeactivateUrl, {ids: ids}, function(data) {
            if (data.success) {
                $.pjax.reload({container: '#popular-college-grid'});
                $('.stats-number.active').text(data.active);
                $('.stats-number.inactive').text(data.inactive);
            }
        });
    }
});

// Update selected count
$(document).on('change', 'input[name="selection[]"]', function() {
    var count = $('input[name="selection[]"]:checked').length;
    $('#selected-count').text(count + ' items selected');
});
JS;

$this->registerJs($script);
?>