<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\web\JsExpression;
use common\models\Stream;
use common\models\Degree;
use common\models\State;

/* @var $this yii\web\View */
/* @var $model backend\models\PopularCollegeSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="popular-college-search">
    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
        'options' => [
            'class' => 'form-horizontal',
            'id' => 'popular-college-search-form',
            'data-pjax' => true
        ],
        'fieldConfig' => [
            'template' => '<div class="col-sm-3 control-label">{label}</div><div class="col-sm-9">{input}{error}</div>',
            'labelOptions' => ['class' => 'control-label'],
        ],
    ]); ?>

    <?php
    // Register JavaScript for auto-filtering
    $this->registerJs("
        // Auto-submit form when filter values change
        $('#popular-college-search-form select, #popular-college-search-form input').on('change keyup', function() {
            // Add small delay for text inputs to avoid too many requests
            if ($(this).is('input[type=text]') || $(this).is('input[type=number]')) {
                clearTimeout(window.searchTimeout);
                window.searchTimeout = setTimeout(function() {
                    $.pjax.reload({
                        container: '#popular-college-grid',
                        data: $('#popular-college-search-form').serialize()
                    });
                }, 500); // 500ms delay for text inputs
            } else {
                // Immediate update for dropdowns
                $.pjax.reload({
                    container: '#popular-college-grid',
                    data: $('#popular-college-search-form').serialize()
                });
            }
        });

        // Handle Select2 change events specifically
        $(document).on('select2:select select2:unselect select2:clear', '#popular-college-search-form .auto-filter-select2', function() {
            $.pjax.reload({
                container: '#popular-college-grid',
                data: $('#popular-college-search-form').serialize()
            });
        });

        // Handle regular dropdown changes
        $(document).on('change', '#popular-college-search-form .auto-filter-dropdown', function() {
            $.pjax.reload({
                container: '#popular-college-grid',
                data: $('#popular-college-search-form').serialize()
            });
        });

        // Show loading indicator during PJAX requests
        $(document).on('pjax:start', function() {
            $('#popular-college-grid').addClass('loading');
            $('#popular-college-grid').append('<div class=\"loading-overlay\"><i class=\"fa fa-spinner fa-spin\"></i> Loading...</div>');
        });

        $(document).on('pjax:end', function() {
            $('#popular-college-grid').removeClass('loading');
            $('.loading-overlay').remove();
        });
    ");
    ?>

    <div class="row">
        <div class="col-md-4">
            <?= $form->field($model, 'college_id')->widget(Select2::class, [
                'data' => $model->college_id ? [$model->college_id => $model->college ? $model->college->name : ''] : [],
                'options' => [
                    'placeholder' => 'Search college...',
                    'multiple' => false,
                    'class' => 'auto-filter-select2'
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'ajax' => [
                        'url' => ['../ajax/college-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('College'); ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'stream_id')->widget(Select2::class, [
                'data' => ArrayHelper::map(Stream::find()->where(['status' => Stream::STATUS_ACTIVE])->orderBy('name ASC')->all(), 'id', 'name'),
                'options' => [
                    'placeholder' => 'Select stream...',
                    'multiple' => false,
                    'class' => 'auto-filter-select2'
                ],
                'pluginOptions' => [
                    'allowClear' => true
                ],
            ])->label('Stream'); ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'degree_id')->widget(Select2::class, [
                'data' => ArrayHelper::map(Degree::find()->where(['status' => Degree::STATUS_ACTIVE])->orderBy('name ASC')->all(), 'id', 'name'),
                'options' => [
                    'placeholder' => 'Select level...',
                    'multiple' => false,
                    'class' => 'auto-filter-select2'
                ],
                'pluginOptions' => [
                    'allowClear' => true
                ],
            ])->label('Level'); ?>
        </div>

    </div>

    <div class="row">
        <div class="col-md-4">
            <?= $form->field($model, 'state_id')->widget(Select2::class, [
                'data' => ArrayHelper::map(State::find()->where(['status' => State::STATUS_ACTIVE])->orderBy('name ASC')->all(), 'id', 'name'),
                'options' => [
                    'placeholder' => 'Select state...',
                    'multiple' => false,
                    'class' => 'auto-filter-select2'
                ],
                'pluginOptions' => [
                    'allowClear' => true
                ],
            ])->label('State'); ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'status')->dropDownList([
                1 => 'Active',
                0 => 'Inactive'
            ], [
                'prompt' => 'All Status',
                'class' => 'form-control auto-filter-dropdown'
            ])->label('Status'); ?>
        </div>
    </div>

    <div class="form-group">
        <div class="col-sm-offset-3 col-sm-9">
            <div class="pull-right">
                <?= Html::a('<i class="fa fa-refresh"></i> Clear All Filters', ['index'], [
                    'class' => 'btn btn-default btn-sm btn-flat',
                    'title' => 'Reset all filters'
                ]) ?>
            </div>
        </div>
    </div>

    <?php ActiveForm::end(); ?>
</div>