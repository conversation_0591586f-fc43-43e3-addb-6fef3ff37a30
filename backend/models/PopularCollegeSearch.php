<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\PopularCollege;

/**
 * PopularCollegeSearch represents the model behind the search form of `common\models\PopularCollege`.
 */
class PopularCollegeSearch extends PopularCollege
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'college_id', 'stream_id', 'degree_id', 'city_id', 'state_id', 'position', 'status'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = PopularCollege::find()->with(['college', 'stream', 'degree', 'city', 'state']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'defaultOrder' => ['position' => SORT_ASC]
            ]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'college_id' => $this->college_id,
            'stream_id' => $this->stream_id,
            'degree_id' => $this->degree_id,
            'city_id' => $this->city_id,
            'state_id' => $this->state_id,
            'position' => $this->position,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        return $dataProvider;
    }
}
