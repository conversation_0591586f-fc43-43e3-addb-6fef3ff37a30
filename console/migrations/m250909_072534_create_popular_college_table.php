<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%popular_college}}`.
 */
class m250909_072534_create_popular_college_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $options = null;
        if (\Yii::$app->db->getDriverName() === 'mysql') {
            $options = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%popular_college}}', [
            'id' => $this->primaryKey(),
            'college_id' => $this->integer()->notNull(),
            'stream_id' => $this->integer()->notNull(),
            'degree_id' => $this->integer()->notNull(),
            'city_id' => $this->integer()->notNull(),
            'state_id' => $this->integer()->notNull(),
            'position' => $this->integer()->defaultValue(1)->notNull(),
            'status' => $this->tinyInteger()->notNull()->defaultValue(1),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
        ], $options);

        // creates index for column `college_id`
        $this->createIndex(
            '{{%idx-popular_college-college_id}}',
            '{{%popular_college}}',
            'college_id'
        );

        // add foreign key for table `{{%popular_college}}`
        $this->addForeignKey(
            '{{%fk-popular_college-college_id}}',
            '{{%popular_college}}',
            'college_id',
            '{{%college}}',
            'id',
            'CASCADE'
        );

        // creates index for column `stream_id`
        $this->createIndex(
            '{{%idx-popular_college-stream_id}}',
            '{{%popular_college}}',
            'stream_id'
        );

        // add foreign key for table `{{%popular_college}}`
        $this->addForeignKey(
            '{{%fk-popular_college-stream_id}}',
            '{{%popular_college}}',
            'stream_id',
            '{{%stream}}',
            'id',
            'CASCADE'
        );

        // creates index for column `degree_id`
        $this->createIndex(
            '{{%idx-popular_college-degree_id}}',
            '{{%popular_college}}',
            'degree_id'
        );

        // add foreign key for table `{{%popular_college}}`
        $this->addForeignKey(
            '{{%fk-popular_college-degree_id}}',
            '{{%popular_college}}',
            'degree_id',
            '{{%degree}}',
            'id',
            'CASCADE'
        );

        // creates index for column `city_id`
        $this->createIndex(
            '{{%idx-popular_college-city_id}}',
            '{{%popular_college}}',
            'city_id'
        );

        // add foreign key for table `{{%popular_college}}`
        $this->addForeignKey(
            '{{%fk-popular_college-city_id}}',
            '{{%popular_college}}',
            'city_id',
            '{{%city}}',
            'id',
            'CASCADE'
        );

        // creates index for column `state_id`
        $this->createIndex(
            '{{%idx-popular_college-state_id}}',
            '{{%popular_college}}',
            'state_id'
        );

        // add foreign key for table `{{%popular_college}}`
        $this->addForeignKey(
            '{{%fk-popular_college-state_id}}',
            '{{%popular_college}}',
            'state_id',
            '{{%state}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey(
            '{{%fk-popular_college-college_id}}',
            '{{%popular_college}}'
        );
        $this->dropForeignKey(
            '{{%fk-popular_college-stream_id}}',
            '{{%popular_college}}'
        );
        $this->dropForeignKey(
            '{{%fk-popular_college-degree_id}}',
            '{{%popular_college}}'
        );
        $this->dropForeignKey(
            '{{%fk-popular_college-city_id}}',
            '{{%popular_college}}'
        );
        $this->dropForeignKey(
            '{{%fk-popular_college-state_id}}',
            '{{%popular_college}}'
        );
        $this->dropIndex(
            '{{%idx-popular_college-college_id}}',
            '{{%popular_college}}'
        );
        $this->dropIndex(
            '{{%idx-popular_college-stream_id}}',
            '{{%popular_college}}'
        );
        $this->dropIndex(
            '{{%idx-popular_college-degree_id}}',
            '{{%popular_college}}'
        );
        $this->dropIndex(
            '{{%idx-popular_college-city_id}}',
            '{{%popular_college}}'
        );
        $this->dropIndex(
            '{{%idx-popular_college-state_id}}',
            '{{%popular_college}}'
        );
        
        $this->dropTable('{{%popular_college}}');
    }
}
