<?php

namespace console\controllers;

use common\helpers\CollegeHelper;
use Exception;
use common\helpers\ConsoleHelper;
use common\helpers\ContentHelper;
use common\models\AlternateCtaText;
use common\models\CollegeNotificationUpdate;
use common\models\ExamContent;
use Yii;
use common\models\SeoInfo;
use common\models\BoardPages;
use common\models\City;
use common\models\College;
use common\models\CollegeContent;
use common\models\CollegeCourseContent;
use common\models\CollegeProgram;
use common\models\Course;
use common\models\CutOff;
use common\models\Degree;
use common\models\documents\CollegeProgram as DocumentsCollegeProgram;
use common\models\PopularCollege;
use common\models\State;
use common\models\Stream;
use common\services\CollegeService;
use DOMDocument;
use DOMXPath;
use frontend\helpers\Url;
use MatthiasMullie\Minify;

class ImportController extends \yii\console\Controller
{
    public function actionGenerateAssets()
    {
        self::minifyAndCopyFiles(Yii::getAlias('@frontend') . '/web/yas/css/version2/', Yii::getAlias('@frontend') . '/web/yas/css/version2/min/');
        self::minifyAndCopyFiles(Yii::getAlias('@frontend') . '/web/yas/js/version2/', Yii::getAlias('@frontend') . '/web/yas/js/version2/min/');
        echo 'Assets Generated!';
    }


    public static function minifyAndCopyFiles($sourceDir, $targetDir)
    {
        $iterator = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($sourceDir, \RecursiveDirectoryIterator::SKIP_DOTS));

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $filePath = $file->getPathname();
                $relativePath = str_replace($sourceDir, '', $filePath);
                $targetPath = $targetDir . $relativePath;

                if (strpos($filePath, 'min') !== false) {
                    continue;
                }

                // Determine file type (CSS or JS) and create minified version
                $extension = pathinfo($file->getFilename(), PATHINFO_EXTENSION);

                if ($extension === 'css') {
                    $minifier = new Minify\CSS($filePath);
                    $minifier->minify($targetPath);
                } elseif ($extension === 'js') {
                    $minifier = new Minify\JS($filePath);
                    $minifier->minify($targetPath);
                }
            }
        }
    }

    public function fetchGoogleDocContent($url)
    {
        try {
            $exportUrl = $this->getGoogleDocExportUrl($url);

            $client = new \GuzzleHttp\Client([
                'headers' => [
                    'User-Agent' => 'Mozilla/5.0', // Mimic a browser request
                ]
            ]);

            $response = $client->get($exportUrl, ['http_errors' => false]);

            if ($response->getStatusCode() !== 200) {
                return 'Error fetching content: HTTP ' . $response->getStatusCode();
            }

            $htmlContent = (string) $response->getBody();

            if (empty(trim($htmlContent))) {
                return 'Error: Empty response from Google Docs';
            }

            // Clean up unnecessary <p> and <span> tags
            return $this->cleanGoogleDocsHTML($htmlContent);
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    public function getGoogleDocExportUrl($url)
    {
        if (preg_match('/\/document\/d\/([a-zA-Z0-9_-]+)/', $url, $matches)) {
            return "https://docs.google.com/document/d/{$matches[1]}/export?format=html";
        }
        return $url; // Return original URL if no match
    }

    public function cleanGoogleDocsHTML($html)
    {
        $html = preg_replace('/<meta[^>]+>/i', '', $html); // Remove <meta> tags
        $html = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $html); // Remove <script> tags
        $html = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $html); // Remove <style> tags

        return $this->removeExtraPTags($html);
    }

    public function removeExtraPTags($html)
    {
        $dom = new DOMDocument();
        libxml_use_internal_errors(true); // Suppress warnings due to malformed HTML

        if (!$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'))) {
            return 'Error: Unable to parse HTML.';
        }

        libxml_clear_errors();

        $xpath = new DOMXPath($dom);

        // Remove <p class="c0">
        foreach ($xpath->query('//p[contains(@class, "c0") or contains(@class, "c1")]') as $p) {
            while ($p->hasChildNodes()) {
                $p->parentNode->insertBefore($p->firstChild, $p);
            }
            $p->parentNode->removeChild($p);
        }

        // Remove <span class="c1">
        foreach ($xpath->query('//span[contains(@class, "c0") or contains(@class, "c1")]') as $span) {
            while ($span->hasChildNodes()) {
                $span->parentNode->insertBefore($span->firstChild, $span);
            }
            $span->parentNode->removeChild($span);
        }

        return $dom->saveHTML();
    }


    public function importSummaryReport($row, $failedRecords, $records, $notUpdatedRecords = [])
    {
        // Summary Report
        echo "\n==== Import Summary ====\n";
        $summaryData = [
            [
                'Total Records' => $row,
                'Updated/Inserted Records' => count($records), // Only count truly updated ones
                'Failed Records' => count($failedRecords)
            ]
        ];
        echo ConsoleHelper::table($summaryData);

        if (!empty($records)) {
            echo "\n==== Inserted Records ====\n";
            echo ConsoleHelper::table($records);
        }

        if (!empty($failedRecords)) {
            echo "\n==== Failed Records ====\n";
            echo ConsoleHelper::table($failedRecords);
        }

        if (!empty($notUpdatedRecords)) {
            echo "\n==== Not Updated Records ====\n";
            echo "\n==== Not Updated Records ====\n";
            $notUpdatedTable = [];

            foreach ($notUpdatedRecords as $id) {
                $notUpdatedTable[] = ['id' => $id];
            }

            echo ConsoleHelper::table($notUpdatedTable);
        }

        echo 'All done!' . PHP_EOL;
    }

    public function actionUpdateExamParentIdSeoTable()
    {
        $success = [];
        $failed = [];

        $examContent = ExamContent::find()
            ->select(['id', 'exam_id', 'slug', 'parent_id'])
            ->where(['NOT', ['parent_id' => null]])
            ->all();

        foreach ($examContent as $content) {
            $seoModel = SeoInfo::find()
                ->where(['entity' => SeoInfo::ENTITY_EXAM, 'entity_id' => $content->exam_id, 'page' => $content->slug])
                ->andWhere(['parent_id' => null])
                ->one();

            if (!$seoModel) {
                continue;
            }

            $seoModel->parent_id = $content->parent_id;

            if ($seoModel->save()) {
                $success[] = [
                    'SeoInfo ID' => $seoModel->id,
                    'Exam ID' => $content->exam_id,
                    'Slug' => $content->slug,
                    'Parent ID Set' => $content->parent_id,
                ];
            } else {
                $failed[] = [
                    'Exam ID' => $content->exam_id,
                    'Slug' => $content->slug,
                    'Errors' => $seoModel->getErrors(),
                ];
            }
        }
        echo "\n=== Successful Updates ===\n";
        foreach ($success as $row) {
            echo "SeoInfo ID: {$row['SeoInfo ID']}, Exam ID: {$row['Exam ID']}, Slug: {$row['Slug']}, Parent ID: {$row['Parent ID Set']}\n";
        }

        echo "\n=== Failed Updates ===\n";
        foreach ($failed as $row) {
            echo "Exam ID: {$row['Exam ID']}, Slug: {$row['Slug']}, Errors: " . json_encode($row['Errors']) . "\n";
        }

        echo "\nSummary: \n";
        echo 'Total Records Processed: ' . count($examContent) . "\n";
        echo 'Successful Updates: ' . count($success) . "\n";
        echo 'Failed Updates: ' . count($failed) . "\n";
    }

    //upload data into college_notification_update table
    public function actionUpdateCollegeNotification()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'cut_off_notification_new.csv';

        if (($handle = fopen($file, 'r')) === false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        $summary = [];

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $collegeId = trim($fileop[0]);
            $title = ContentHelper::removeStyleTag(stripslashes(html_entity_decode($fileop[1])));
            $content = preg_replace('/^<p>|<\/p>$/i', '', trim($fileop[2]));
            $page = $fileop[3];

            if (empty($page)) {
                continue;
            }

            $startDate = \Carbon\Carbon::createFromFormat('m/d/Y', trim($fileop[4]))->format('Y-m-d');
            $endDate = \Carbon\Carbon::createFromFormat('m/d/Y', trim($fileop[5]))->format('Y-m-d');

            $model = new CollegeNotificationUpdate();
            $model->college_id = $collegeId;
            $model->text = $title;
            $model->content = $content;
            $model->sub_page = $page;
            $model->start_date = $startDate;
            $model->end_date = $endDate;
            $model->publish_at = $startDate;
            $model->status = CollegeNotificationUpdate::STATUS_ACTIVE;

            if ($model->save()) {
                echo "Saved: CollegeID: $collegeId | Page: $page\n";

                // Add to summary
                if (!isset($summary[$collegeId])) {
                    $summary[$collegeId] = [];
                }
                $summary[$collegeId][] = $page;
            } else {
                echo "Error: CollegeID: $collegeId | Page: $page\n";
                print_r($model->getErrors());
            }

            $i++;
        }

        echo "\n--- Summary ---\n";
        foreach ($summary as $collegeId => $pages) {
            $count = count($pages);
            $pageList = implode(', ', $pages);
            echo "College ID: $collegeId | Pages: [$pageList] | Total: $count\n";
        }
    }

    public function actionInsertBoardPages()
    {
        $subPages = [
            'overview' => 'Overview',
            'date-sheet' => 'Date Sheet',
            // 'time-table' => 'Time Table',
            // 'routine' => 'Routine',
            'registration-form' => 'Registration Form',
            'application-form' => 'Application Form',
            'registration-card' => 'Registration card',
            'admit-card' => 'Admit Card',
            // 'hall-ticket' => 'Hall Ticket',
            'syllabus' => 'Syllabus',
            'exam-centres' => 'Exam Centres',
            'marking-scheme' => 'Marking Scheme',
            'results' => 'Results',
            'supplementary' => 'Supplementary',
            'sample-papers' => 'Sample Papers',
            'previous-year-question-papers' => 'Previous Year Question Papers',
            'solved-question-papers' => 'Solved Question Papers',
            // 'reference-books' => 'Reference Books',
            'exam-pattern' => 'Exam Pattern',
            'preparation' => 'Preparation',
            'answer-key' => 'Answer Key',
            'books' => 'Books',
            'toppers' => 'Toppers',
            'notes' => 'Notes',
            'question-paper' => 'Question Paper',
            'passing-marks' => 'Passing Marks',
            'grading-system' => 'Grading System',
            'marksheet' => 'Marksheet',
            'practical-exam-datesheet' => 'Practical Exam Datesheet',
            'roll-number-finder' => 'Roll Number Finder',
            'deleted-syllabus' => 'Deleted Syllabus'
        ];
        $boardDefaultSeoInfo = [
            'overview' => [
                'h1' => '{board-name} Exam {year}',
                'title' => '{board-name} Exam {year} | Check Exam Date, Time Table, Admit Card, Syllabus, Result',
                'description' => 'Everything you need to know about the {board-name} examination {year}. Get latest updates on {board-name} syllabus, timetable, results, and more.'
            ],
            'date-sheet' => [
                'h1' => '{board-name} Time Table {year}',
                'title' => '{board-name} Time Table {year} | Check Updated Date Sheet Here',
                'description' => 'Check out the latest updates on {board-name} date sheet {year}. Students can view and download the official date sheet for the {year} exam here.'
            ],
            'syllabus' => [
                'h1' => '{board-name} Syllabus {year}: Detailed Term-Wise Topics, Important Chapters, Marking Scheme',
                'title' => '{board-name} Syllabus {year} | Detailed Term-Wise Topics, Important Chapters, Marking Scheme',
                'description' => 'Get {board-name} syllabus for all subjects. Find the latest syllabus issued by {board-name} and detailed term-wise topics, important chapters, and the marking scheme.'
            ],
            'registration-form' => [
                'h1' => '{board-name} Registration {year}',
                'title' => '{board-name} Registration {year} | Check {board-name} Registration {year} Dates, Process and Fees',
                'description' => 'Get updates on the {board-name} registration process and check out the detailed registration fee structure and other important information.'
            ],
            'admit-card' => [
                'h1' => '{board-name} Admit Card {year}',
                'title' => 'Download {board-name} Admit Card {year} | Check Release Date, Steps to download admit card',
                'description' => 'Get all updates on {board-name} admit card {year}. Check the release date and the procedure to collect {board-name} admit card {year}.'
            ],
            'exam-centres' => [
                'h1' => '{board-name} Exam Centre List {year}',
                'title' => '{board-name} Exam {year} Centres - Check List Here',
                'description' => '{board-name} updates the list of examination centres every year. Check out the important details regarding the {board-name} exam centres here.'
            ],
            'results' => [
                'h1' => '{board-name} Result {year}',
                'title' => 'Check {board-name} Result {year}  Release Date, Download Marksheet',
                'description' => 'Find out the {board-name} result release date and download your marksheet/scorecard. Check {board-name} exam results {year} using the direct link provided.'
            ],
            'sample-papers' => [
                'h1' => '{board-name} Sample Paper {year} - Download Free PDF',
                'title' => 'Download {board-name} Sample Paper {year} PDF',
                'description' => 'Start preparing for the {board-name} final exams by practising the sample papers. Download {board-name} sample question papers for free.'
            ],
            'previous-year-question-papers' => [
                'h1' => '{board-name} Previous Year Question Papers {year}',
                'title' => 'Download {board-name} Previous Year Question Papers {year} PDF',
                'description' => '<Boards Name> previous year papers will help you analyse the exam pattern and level of questions. Practice these previous year papers to score well.'
            ],
            'solved-question-papers' => [
                'h1' => '{board-name} Solved Question Papers {year}',
                'title' => 'Download {board-name} Solved Question Papers {year} PDF',
                'description' => 'Solving more questions will improve time management skills for exams. Download {board-name} solved question papers now and practise.'
            ],
            'application-form' => [
                'h1' => '{board-name} Application Form {year}',
                'title' => 'Check {board-name} Application Form {year} Dates, Process and Fees',
                'description' => 'Get updates on the {board-exam-year} application process and check out the details of the fee structure and other important information.'
            ],
            'hall-ticket' => [
                'h1' => '{board-name} Hall Ticket {year}',
                'title' => 'Download {board-name} Hall Ticket {year} | Check Release Date, Steps to download Hall Ticket',
                'description' => 'Get complete details on {board-name} hall ticket {year}. Check the release date and the procedure to collect {board-name} admit card {year}.'
            ],
            'marking-scheme' => [
                'h1' => '{board-name} {year} Marking Scheme/Grading Pattern',
                'title' => '{board-name} {year} Marking Scheme/Grading Pattern - Minimum Marks to Qualify',
                'description' => 'Get complete details about the {board-name} marking scheme {year}. Know the minimum marks to qualify according to the latest marking scheme.'
            ],
            'reference-books' => [
                'h1' => 'Best Reference Books for {board-name} Exam Praparation',
                'title' => 'Best Reference Books for {board-name} Exam Praparation',
                'description' => 'Get detailed information on {board-name} reference books for the {year} academic year. Download the textbooks and prepare for {board-name} examination {year}.'
            ],
            'routine' => [
                'h1' => '{board-name} Routine {year}',
                'title' => '{board-name} Routine {year} | Check Updated Routine Here',
                'description' => 'Check out the latest information on {board-name} routine {year}. Students can view and download the official routine for the {year} exam here.'
            ],
            'supplementary' => [
                'h1' => '{board-name} Supplementary Exam {year} - Date Sheet, Result {year}',
                'title' => '{board-name} Supplementary Exam {year} | Check Supplementary Exam Date Sheet, Admit Card, Result {year}',
                'description' => 'Get complete information on {board-name} supplementary exam {year}. Check out the updated syllabus, timetable, admit card, results, and more.'
            ],
            'time-table' => [
                'h1' => '{board-name} Time Table {year}',
                'title' => '{board-name} Time Table {year} | Check Updated Time Table Here',
                'description' => 'Check out the latest information on {board-name} time table {year}. Students can view and download the official timetable for the {year} exam here.'
            ],
            'toppers' => [
                'h1' => '{board-name} Class # Toppers List {year}: Rank, Marks, School Code',
                'title' => '{board-name} Class # Toppers List {year}: Rank, Marks, School Code',
                'description' => '{board-name} has released the topper’s list and pass percentage along with Class 12th result. Check out stream-wise toppers, pass percentage and result details here.'
            ],
            'notes' => [
                'h1' => '{board-name} english notes {year}: Download PDF',
                'title' => '{board-name} English Notes {year}: Download PDF',
                'description' => '{board-name} Notes includes chapter wise explanations for all the topic provided in the curriculum.'
            ],
            'question-paper' => [
                'h1' => '{board-name} Maths Question Paper {year}',
                'title' => '{board-name} 12 Maths Question Paper {year}',
                'description' => '{board-name} Question Paper {year} is provided in PDF format, which can be downloaded for free. Use the Question Paper {year} to enhance your preparation.'
            ],
            'books' => [
                'h1' => '{board-name} Textbooks: Download PDF',
                'title' => '{board-name} Textbooks: Download PDF',
                'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
            ],
            'syllabus-dropdown' => [
                'h1' => '{board-name} {subject} Syllabus 2023-24: Download PDF',
                'title' => '{board-name} {subject} Syllabus 2023-24: Download PDF',
                'description' => 'The {board-name} {subject} Syllabus 2023-24 consists of Probability, Linear Programming, Vector, Three Dimensional Geometry and more.'
            ],
            'supplementary-time-table' => [
                'h1' => '{board-name} Supplementary Time Table 2023',
                'title' => '{board-name} Supplementary Time Table 2023',
                'description' => '{board-name} 2023 Compartment Date Sheet is released on May 30, 2023. Check the {board-name} latest exam pattern, syllabus, important dates.'
            ],
            'supplementary-date-sheet' => [
                'h1' => '{board-name} Supplementary Time Table 2023',
                'title' => '{board-name} Supplementary Time Table 2023',
                'description' => '{board-name} 2023 Compartment Date Sheet is released on May 30, 2023. Check the {board-name} latest exam pattern, syllabus, important dates.'
            ],
            'supplementary-admit-card' => [
                'h1' => '{board-name} Compartment Exam Admit Card 2023',
                'title' => '{board-name} Compartment Exam Admit Card 2023',
                'description' => 'Check out the release date of {board-name} Compartment Admit Card 2023 for the exam scheduled on July 17, 2023 '
            ],
            'supplementary-hall-ticket' => [
                'h1' => '{board-name} Compartment Exam Admit Card 2023',
                'title' => '{board-name} Compartment Exam Admit Card 2023',
                'description' => 'Check out the release date of {board-name} Compartment Admit Card 2023 for the exam scheduled on July 17, 2023'
            ],
            'supplementary-result' => [
                'h1' => '{board-name} Compartment Result 2023: Steps to Check',
                'title' => '{board-name} Compartment Result 2023: Steps to Check',
                'description' => '{board-name} Supplementary Result 2023 has been declared on 20th June 2023. The direct link to check Supplementary Result 2023 is shared here as the result has been announced'
            ],
            'answer-key-dropdown' => [
                'h1' => '{board-name} {subject} Answer Key 2023, Set 1,2,3 Paper Solution: Download PDF',
                'title' => '{board-name} {subject} Answer Key 2023, Set 1,2,3 Paper Solution: Download PDF',
                'description' => '{board-name} {subject} Answer Key 2023 and Paper Solution of Both the papers Basic and Standard. Check the {subject} Answer Key 2023 for Questions and Answers '
            ],
            'answer-key' => [
                'h1' => '{board-name} {subject} Answer Key 2023, Set 1,2,3 Paper Solution: Download PDF',
                'title' => '{board-name} {subject} Answer Key 2023, Set 1,2,3 Paper Solution: Download PDF',
                'description' => '{board-name} {subject} Answer Key 2023 and Paper Solution of Both the papers Basic and Standard. Check the {subject} Answer Key 2023 for Questions and Answers '
            ],
            'notes-dropdown' => [
                'h1' => '{board-name} {subject} Notes 2023: Download PDF',
                'title' => '{board-name} {subject} notes 2023: Download PDF',
                'description' => '{board-name} {subject} Notes includes chapter wise explanations for all the topic provided in the curriculum.'
            ],
            'notes' => [
                'h1' => '{board-name} {subject} Notes 2023: Download PDF',
                'title' => '{board-name} {subject} notes 2023: Download PDF',
                'description' => '{board-name} {subject} Notes includes chapter wise explanations for all the topic provided in the curriculum.'
            ],
            'question-paper-dropdown' => [
                'h1' => '{board-name} {subject} Maths Question Paper 2023',
                'title' => '{board-name} {subject} Maths Question Paper 2023',
                'description' => '{board-name} {subject} Question Paper 2023 is provided in PDF format, which can be downloaded for free. Use the Question Paper 2023 to enhance your preparation.'
            ],
            'question-paper' => [
                'h1' => '{board-name} {subject} Maths Question Paper 2023',
                'title' => '{board-name} {subject} Maths Question Paper 2023',
                'description' => '{board-name} {subject} Question Paper 2023 is provided in PDF format, which can be downloaded for free. Use the Question Paper 2023 to enhance your preparation.'
            ],
            'books-dropdown' => [
                'h1' => '{board-name} {subject} Textbook: Download PDF',
                'title' => '{board-name} {subject} Textbook: Download PDF',
                'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
            ],
            'passing-marks' => [
                'h1' => '{board-name} Passing Marks {year} - Check {board-name} Theory, Practical, Minimum, Maximum Marks',
                'title' => '{board-name} Passing Marks {year} - Check {board-name} Theory, Practical, Minimum, Maximum Marks',
                'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
            ],
            'grading-system' => [
                'h1' => '{board-name} Grading System {year} - Check {board-name} Grading System and Passing Marks',
                'title' => '{board-name} Grading System {year} - Check {board-name} Grading System and Passing Marks',
                'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
            ],
            'marksheet' => [
                'h1' => '{board-name} Marksheet {year} - Download {board-name} Original Mark Sheet Here, Direct Link',
                'title' => '{board-name} Marksheet {year} - Download {board-name} Original Mark Sheet Here, Direct Link',
                'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
            ],
            'practical-exam-datesheet' => [
                'h1' => '{board-name} Practical Exam Date Sheet {year} - Check {board-name} Practical Date Sheet',
                'title' => '{board-name} Practical Exam Date Sheet {year} - Check {board-name} Practical Date Sheet',
                'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
            ],
            'roll-number-finder' => [
                'h1' => '{board-name} Practical Exam Date Sheet {year} - Check {board-name} Practical Date Sheet',
                'title' => '{board-name} Practical Exam Date Sheet {year} - Check {board-name} Practical Date Sheet',
                'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
            ],
            'deleted-syllabus' => [
                'h1' => '{board-name} Deleted Syllabus {year} - Check {board-name} Deleted Syllabus',
                'title' => '{board-name} Deleted Syllabus {year} - Check {board-name} Deleted Syllabus',
                'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
            ],
            'deleted-syllabus-dropdown' => [
                'h1' => '{board-name} Deleted Syllabus {year} - Check {board-name} Deleted Syllabus',
                'title' => '{board-name} Deleted Syllabus {year} - Check {board-name} Deleted Syllabus',
                'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
            ],
            'exam-pattern'  => [
                'h1' => '{board-name} {class} {subject-name} exam pattern {year} with Marking Scheme and Topic-wise Weightage',
                'title' => '{boards-name} {class} {subject-name} exam pattern {year} with Marking Scheme and Topic-wise Weightage',
                'description' => 'Exam pattern {board-name} with Marking Scheme and Topic-wise Weightage.'

            ],
            'exam-pattern-dropdown'  => [
                'h1' => '{board-name} {subject-name} Exam Pattern {year} with Marking Scheme and Topic-wise Weightage',
                'title' => '{board-name} {subject-name} Exam Pattern {year} with Marking Scheme and Topic-wise Weightage',
                'description' => 'Exam pattern {board-name} with Marking Scheme and Topic-wise Weightage.'

            ],
            'previous-year-question-papers-dropdown'  => [
                'h1' => '{board-name} {subject-name} Previous Year Question Papers',
                'title' => '{board-name} {subject-name} Previous Year Question Papers',
                'description' => 'previous year papers will help you analyse the exam pattern and level of questions. Practice these previous year papers to score well..'

            ],
            'sample-papers-dropdown'  => [
                'h1' => '{board-name} {subject-name} Sample Paper - Download PDF',
                'title' => '{board-name} {subject-name} Sample Paper - Download PDF',
                'description' => 'Start preparing for the {board-name} final exams by practising the sample papers. Download {board-name} sample question papers for free..'
            ]
        ];
        $syllabusSubPagesDropDown = [
            ['name' => 'Mathematics', 'value' => 'Mathematics'],
            ['name' => 'Physics', 'value' => 'Physics'],
            ['name' => 'Chemistry', 'value' => 'Chemistry'],
            ['name' => 'Biology', 'value' => 'Biology'],
            ['name' => 'Computer Science', 'value' => 'Computer Science'],
            ['name' => 'Economics', 'value' => 'Economics'],
            ['name' => 'English', 'value' => 'English'],
            ['name' => 'Business Studies', 'value' => 'Business Studies'],
            ['name' => 'Accountancy', 'value' => 'Accountancy'],
            ['name' => 'Hindi', 'value' => 'Hindi'],
            ['name' => 'Home Science', 'value' => 'Home Science'],
            ['name' => 'Physical Education', 'value' => 'Physical Education'],
            ['name' => 'History', 'value' => 'History'],
            ['name' => 'Geography', 'value' => 'Geography'],
            ['name' => 'Political Science', 'value' => 'Political Science'],
            ['name' => 'Psychology', 'value' => 'Psychology'],
            ['name' => 'Sanskrit', 'value' => 'Sanskrit'],
            ['name' => 'Sociology', 'value' => 'Sociology'],
            ['name' => 'Social Science', 'value' => 'Social Science']
        ];
        $supplementarySubPagesDropDown = [
            ['name' => 'Time Table', 'value' => 'Time Table'],
            ['name' => 'Date Sheet', 'value' => 'Date Sheet'],
            ['name' => 'Admit Card', 'value' => 'Admit Card'],
            ['name' => 'Hall Ticket', 'value' => 'Hall Ticket'],
            ['name' => 'Result', 'value' => 'Result']
        ];
        $allowed = ['syllabus', 'notes', 'answer-key', 'question-paper', 'books', 'supplementary', 'deleted-syllabus', 'exam-pattern', 'previous-year-question-papers', 'sample-papers'];
        foreach ($subPages as $key => $pages) {
            $boardPage = new BoardPages();
            $boardPage->name = $pages;
            if (!empty($boardDefaultSeoInfo[$key])) {
                $boardPage->h1 = $boardDefaultSeoInfo[$key]['h1'];
                $boardPage->title = $boardDefaultSeoInfo[$key]['title'];
                $boardPage->description = $boardDefaultSeoInfo[$key]['description'];
                $boardPage->status = BoardPages::STATUS_ACTIVE;
            }
            $boardPage->save();
            if (in_array($key, $allowed)) {
                if ($key == 'supplementary') {
                    foreach ($supplementarySubPagesDropDown as $subPage) {
                        $boardPagesSub = new BoardPages();
                        $boardPagesSub->name = $subPage['name'];
                        $boardPagesSub->h1 = $boardDefaultSeoInfo[$key]['h1'];
                        $boardPagesSub->title = $boardDefaultSeoInfo[$key]['title'];
                        $boardPagesSub->description = $boardDefaultSeoInfo[$key]['description'];
                        $boardPagesSub->parent_id = $boardPage->id;
                        $boardPagesSub->status = BoardPages::STATUS_ACTIVE;
                        $boardPagesSub->save();
                    }
                } else {
                    foreach ($syllabusSubPagesDropDown as $subPage) {
                        $boardPagesSub = new BoardPages();
                        $boardPagesSub->name = $subPage['name'];
                        $boardPagesSub->h1 = $boardDefaultSeoInfo[$key . '-dropdown']['h1'];
                        $boardPagesSub->title = $boardDefaultSeoInfo[$key . '-dropdown']['title'];
                        $boardPagesSub->description = $boardDefaultSeoInfo[$key . '-dropdown']['description'];
                        $boardPagesSub->parent_id = $boardPage->id;
                        $boardPagesSub->status = BoardPages::STATUS_ACTIVE;
                        $boardPagesSub->save();
                    }
                }
            }
        }
    }

    public function actionUpdateAlternateCtaTitle()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'alternate_cta_title.csv';

        if (($handle = fopen($file, 'r')) === false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = AlternateCtaText::find()->where(['id' => $fileop[0]])->one();

            if (!$model) {
                echo 'Not found: ' . $fileop[0] . "\n";
                continue;
            }

            $model->cta_title = $fileop[2];

            if ($model->save()) {
                echo "Saved: CTA: $fileop[0]\n";
            } else {
                print_r($model->getErrors());
            }

            $i++;
        }
    }

    public function actionInsertProgramIdtoCutOffDetail()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'cut_off_notification_new.csv';

        if (($handle = fopen($file, 'r')) === false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $cutOffDetail = CutOff::find()->where(['id' => $fileop[0]])->one();

            if (!$cutOffDetail) {
                continue;
            }

            $cutOffDetail->program_id = $fileop[1];

            if ($cutOffDetail->save()) {
                echo "Saved: cutOffDetailID: $cutOffDetail->id \n";
            } else {
                echo "Error: cutOffDetailID: $cutOffDetail->id \n";
                print_r($cutOffDetail->getErrors());
            }

            $i++;
        }
    }

    public function actionCutOffDataWithIds()
    {
        //$cutOffCategory = CollegeService::getCategory();
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'cut_off_notification_new.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            // dd($fileop);s
            $newModel = new CutOff();
            $newModel->college_id = (int)$fileop[0];
            $newModel->exam_id = (int)$fileop[1];
            $newModel->course_id= (int)$fileop[2];
            if (!empty($fileop[3])) {
                $newModel->specialization_id= (int)$fileop[3];
            }
            $newModel->program_id=  (int)$fileop[4];
            $newModel->program_name = $fileop[5];
            
                $newModel->category = (int)$fileop[6];
            
                $newModel->gender= (int)$fileop[7];
            
                $newModel->type= (int)$fileop[8];
           
                $newModel->year = (int)$fileop[9];
          
                $newModel->round = (int)$fileop[10];
           
                $newModel->opening_rank= !empty($fileop[11]) ? (int)$fileop[11] : '';
           
                $newModel->closing_rank= !empty($fileop[12]) ? (int)$fileop[12] : '';

            if (!empty($fileop[13])) {
                $newModel->percentile = (int)$fileop[13];
            }
            if (!empty($fileop[14])) {
                $newModel->closing_score = (int)$fileop[14];
            }

            if ($newModel->save()) {
                echo "{$fileop[0]} \t {$fileop[1]} \n";
            } else {
                print_r($newModel->getErrors());
            }
        }
    }

    
    public function actionAppendCollegeSubpageUrl()
    {
       // Path to your CSV file
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'data.csv';


        // Data you want to add (each sub-array is a row)
        $header = ['college_id', 'college name', 'sub page id', 'sub page name', 'sub page url'];

        $newData = [];
        $query = College::find()->where(['status' => 1]);

        foreach ($query->batch() as $colleges) {
            foreach ($colleges as $college) {
                $newData = $this->subpageUrl($college->id);
                // if(!empty$newData);
                // print_r($newData);
                if (empty($newData)) {
                    return false;
                }
                // Open the file in append mode ('a')
                if (($handle = fopen($file, 'a')) !== false) {
                    foreach ($newData as $row) {
                        // fputcsv writes the array as a CSV row
                        fputcsv($handle, $row);
                    }
                    fclose($handle);
                    echo "Data added successfully. \n";
                } else {
                    echo 'Error: Unable to open file.';
                }
            }
        }
    }

    public function subpageUrl($collegeId)
    {
        $query = CollegeContent::find()->with('college')->where(['entity_id' => $collegeId]);
        //->andWhere(['status' => 1]);
       
        $collegePages = $query->all();
        if (!empty($collegePages)) {
            foreach ($collegePages as $page) {
                if ($page->parent_id != null) {
                    $collegeContent = CollegeContent::find()->where(['id' => $page->parent_id])->one();
                    $url = CollegeHelper::collegeDropDownUrlFormate($page->college->slug, $page->sub_page, $collegeContent->sub_page);
                } else {
                    $url = CollegeHelper::collegeUrlFormate($page->college->slug, $page->sub_page);
                }

                $url = 'college/' . $url;

                $arr[] =  [
                    $collegeId,
                    $page->college->name,
                    $page->id,
                    $page->sub_page,
                    $url,
                ];
            }
            // dd($arr);
            return $arr ;
        }

        // dd($arr);
    }

    public function actionRemoveCollege()
    {
        // $arr = [
        //     3926, 4193, 4477, 4491, 4994, 5053, 5073, 5100, 5136, 5180, 5186, 5190, 5207,
        //     5236, 5237, 5270, 5286, 5291, 5302, 5332, 5334, 5353, 5363, 5364, 5368, 5432,
        //     5836, 5838, 5848, 6173, 6601, 7248, 8205, 9611, 9753, 9954, 10033, 10041,
        //     10064, 10071, 10083, 10087, 10089, 10097, 10106, 10109, 10112, 10132, 10142,
        //     10143, 10149, 10156, 10159, 10213, 10217, 10247, 10270, 10278, 10279, 10282,
        //     10283, 10287, 10291, 10304, 10329, 10339, 10341, 10361, 10362, 10418, 10485,
        //     10487, 10489, 10492, 10494, 10496, 10499, 10500, 10506, 10510, 10512, 10519,
        //     10521, 10522, 10525, 10528, 10538, 10539, 10540, 10544, 10545, 10549, 10553,
        //     10556, 10565, 10566, 10567
        // ];
    //     $arr = [
    //         10567, 10568, 10570, 10571, 10572, 10574, 10576, 10580, 10582, 10587,
    //     10588, 10589, 10590, 10592, 10594, 10595, 10597, 10601, 10602, 10603,
    //     10604, 10611, 10612, 10614, 10616, 10617, 10618, 10620, 10621, 10623,
    //     10624, 10625, 10627, 10630, 10631, 10633, 10634, 10635, 10636, 10637,
    //     10638, 10639, 10642, 10643, 10646, 10653, 10656, 10657, 10658, 10661,
    //     10663, 10666, 10667, 10670, 10671, 10674, 10675, 10677, 10679, 10680,
    //     10685, 10697, 10698, 10707, 10708, 10711, 10715, 10716, 10718, 10720,
    //     10721, 10722, 10726, 10730, 10731, 10743, 10905, 10910, 10951, 10954,
    //     10980, 10988, 10997, 11005, 11009, 11010, 11012, 11013, 11016, 11018,
    //     11020, 11023, 11024, 11026, 11032, 11037, 11039, 11040, 11043, 11055
    // ];
        $arr = [
        11067, 11114, 11163, 11168, 11169, 11178, 11180, 11184, 11192, 11211,
        11254, 11257, 11289, 11304, 11351, 11355, 11380, 11390, 11392, 11398,
        11472, 11597, 11598, 11599, 11600, 11601, 11602, 11603, 11722, 11825,
        11951, 12182, 12184, 12188, 12190, 12193, 12195, 12308, 12441, 12485,
        12489, 12514, 12517, 12518, 12521, 12523, 12526, 12527, 12528, 12530,
        12531, 12532, 12534, 12535, 12537, 12539, 12541, 12543, 12544, 12545,
        12546, 12549, 12550, 12554, 12557, 12559, 12563, 12566, 12569, 12571,
        12573, 12575, 12576, 12578, 12579, 12580, 12582, 12584, 12586, 12587,
        12589, 12590, 12646, 12704, 12706, 12879, 12880, 12881, 12882, 12884,
        12885, 12887, 12889, 12890, 12892, 12893, 12895, 12898, 12965, 12982,
        12983, 12985, 12997, 13063, 13068, 42062, 42104
        ];
    

        foreach ($arr as $a) {
            $collegeContent = CollegeContent::find()->where(['entity_id' => $a])->andWhere(['status' => 1])->all();
            // dd($collegeContent);
            foreach ($collegeContent as $content) {
                $con = CollegeContent::find()->where(['id' => $content->id])->one();

                if (empty($con)) {
                    return false;
                }

                $con->status = 0;

                if ($con->save()) {
                    echo "{$con->id} \t {$con->sub_page} \n";
                } else {
                    print_r($con->getErrors());
                }
            }

            $college = College::find()->where(['id' => $a])->one();

            if (empty($college)) {
                return false;
            }

            $college->status = 0;

            if ($college->save()) {
                echo "{$college->id} \t {$college->slug} \n";
            } else {
                print_r($college->getErrors());
            }
        }
    }

    //import data into popuar_college table
    public function actionImportPopularCollege()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'popular_college.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $college = College::find()->where(['name' => $fileop[0]])->one();
            if (empty($college)) {
                continue;
            }

            $stream = Stream::find()->where(['name' => $fileop[1]])->one();
            if (empty($stream)) {
                continue;
            }

            $degree = Degree::find()->where(['name' => $fileop[2]])->one();
            if (empty($degree)) {
                continue;
            }

            $city = City::find()->where(['name' => $fileop[3]])->one();
            if (empty($city)) {
                continue;
            }

            $state = State::find()->where(['name' => $fileop[4]])->one();
            if (empty($state)) {
                continue;
            }

            $model = PopularCollege::find()->where(['college_id' => $college->id, 'stream_id' => $stream->id, 'degree_id' => $degree->id, 'city_id' => $city->id, 'state_id' => $state->id])->one();
            if (!empty($model)) {
                continue;
            }

            $model = new PopularCollege();
            $model->college_id = $college->id;
            $model->stream_id = $stream->id;
            $model->degree_id = $degree->id;
            $model->city_id = $city->id;
            $model->state_id = $state->id;
            $model->position = $fileop[5];
            $model->status = 1;
            $model->created_at = date('Y-m-d H:i:s');
            $model->updated_at = date('Y-m-d H:i:s');

            if ($model->save()) {
                echo "{$model->id} \t {$model->college->name} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }
}
