<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[\common\models\PopularCollege]].
 *
 * @see \common\models\PopularCollege
 */
class PopularCollegeQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return \common\models\PopularCollege[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\PopularCollege|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
